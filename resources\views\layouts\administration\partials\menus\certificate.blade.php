<!-- Certificate Management -->
@canany(['Certificate Everything', 'Certificate Create', 'Certificate Read', 'Certificate Update', 'Certificate Delete'])
    <li class="menu-item {{ request()->is('certificate*') ? 'active open' : '' }}">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-certificate"></i>
            <div data-i18n="Certificate">{{ __('Certificate') }}</div>
        </a>
        <ul class="menu-sub">
            @canany(['Certificate Everything', 'Certificate Read'])
                <li class="menu-item {{ request()->is('certificate/all*') ? 'active' : '' }}">
                    <a href="{{ route('administration.certificate.index') }}" class="menu-link">{{ __('All Certificates') }}</a>
                </li>
            @endcanany
            @canany(['Certificate Everything', 'Certificate Create'])
                <li class="menu-item {{ request()->is('certificate/create*') ? 'active' : '' }}">
                    <a href="{{ route('administration.certificate.create') }}" class="menu-link">{{ __('Generate Certificate') }}</a>
                </li>
            @endcanany
            @canany(['Certificate Everything', 'Certificate Read', 'Certificate Update', 'Certificate Delete'])
                <li class="menu-item {{ request()->is('certificate/template*') ? 'active open' : '' }}">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Templates">{{ __('Templates') }}</div>
                    </a>
                    <ul class="menu-sub">
                        @canany(['Certificate Everything', 'Certificate Read'])
                            <li class="menu-item {{ request()->is('certificate/template/all*') ? 'active' : '' }}">
                                <a href="{{ route('administration.certificate.template.index') }}" class="menu-link">
                                    <div data-i18n="All Templates">{{ __('All Templates') }}</div>
                                </a>
                            </li>
                        @endcanany
                        @canany(['Certificate Everything', 'Certificate Create'])
                            <li class="menu-item {{ request()->is('certificate/template/create*') ? 'active' : '' }}">
                                <a href="{{ route('administration.certificate.template.create') }}" class="menu-link">
                                    <div data-i18n="Create Template">{{ __('Create Template') }}</div>
                                </a>
                            </li>
                        @endcanany
                    </ul>
                </li>
            @endcanany
        </ul>
    </li>
@endcanany
