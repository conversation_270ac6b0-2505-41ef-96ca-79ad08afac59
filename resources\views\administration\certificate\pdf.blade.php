<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $certificate->certificate_type }} - {{ $certificate->employee->user->name }}</title>
    <style>
        @page {
            margin: 40px;
            size: A4;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .certificate-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            border: 3px solid #2c5aa0;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            min-height: 600px;
        }
        
        .certificate-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 20px;
        }
        
        .company-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            display: block;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c5aa0;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .company-address {
            font-size: 14px;
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .certificate-title {
            text-align: center;
            margin: 40px 0;
        }
        
        .certificate-title h1 {
            font-size: 36px;
            color: #2c5aa0;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 3px;
            text-decoration: underline;
        }
        
        .certificate-content {
            font-size: 16px;
            line-height: 1.8;
            text-align: justify;
            margin: 40px 0;
            padding: 0 20px;
        }
        
        .employee-name {
            font-weight: bold;
            color: #2c5aa0;
            font-size: 18px;
        }
        
        .certificate-footer {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .signature-section {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-bottom: 10px;
            width: 100%;
        }
        
        .signature-title {
            font-weight: bold;
            font-size: 14px;
        }
        
        .signature-name {
            font-size: 12px;
            color: #666;
        }
        
        .certificate-date {
            text-align: right;
            font-size: 14px;
            color: #666;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            color: rgba(44, 90, 160, 0.05);
            font-weight: bold;
            z-index: -1;
            pointer-events: none;
        }
        
        .certificate-id {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 12px;
            color: #666;
        }
        
        .decorative-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 1px solid #2c5aa0;
            border-radius: 8px;
            pointer-events: none;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="decorative-border"></div>
        <div class="watermark">{{ config('app.name') }}</div>
        
        <div class="certificate-id">
            Certificate ID: {{ str_pad($certificate->id, 6, '0', STR_PAD_LEFT) }}
        </div>
        
        <div class="certificate-header">
            {{-- Company Logo --}}
            @if(config('app.logo'))
                <img src="{{ public_path(config('app.logo')) }}" alt="Company Logo" class="company-logo">
            @endif
            
            <h2 class="company-name">{{ config('app.name') }}</h2>
            @if(config('app.address'))
                <p class="company-address">{{ config('app.address') }}</p>
            @endif
        </div>
        
        <div class="certificate-title">
            <h1>{{ $certificate->certificate_type }}</h1>
        </div>
        
        <div class="certificate-content">
            {!! nl2br(e($certificate->content)) !!}
        </div>
        
        <div class="certificate-footer">
            <div class="signature-section">
                <div class="signature-line"></div>
                <div class="signature-title">Authorized Signature</div>
                <div class="signature-name">{{ $certificate->creator->name }}</div>
                <div class="signature-name">{{ $certificate->creator->employee->alias_name ?? '' }}</div>
            </div>
            
            <div class="certificate-date">
                <strong>Date:</strong> {{ $certificate->created_at->format('F d, Y') }}
            </div>
        </div>
    </div>
</body>
</html>
