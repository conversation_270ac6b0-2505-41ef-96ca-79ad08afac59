<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create Certificate Template')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .placeholder-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .placeholder-item {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 4px;
            border: 1px solid #e3e6f0;
        }
        .placeholder-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create Certificate Template')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.certificate.template.index')); ?>"><?php echo e(__('Certificate Templates')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Create Template')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Instructions Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="ti ti-info-circle me-2"></i><?php echo e(__('How to Create Certificate Templates')); ?></h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary"><?php echo e(__('Step 1: Template Information')); ?></h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Enter a descriptive template title')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Add an optional description for reference')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Keep the template active for immediate use')); ?></li>
                        </ul>

                        <h6 class="text-primary mt-3"><?php echo e(__('Step 2: Template Content')); ?></h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Write your certificate content in the text area')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Use placeholders for dynamic employee data')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Click placeholders from the right panel to insert them')); ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary"><?php echo e(__('Step 3: Using Placeholders')); ?></h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-lightbulb text-warning me-2"></i><?php echo e(__('Placeholders are replaced with actual employee data')); ?></li>
                            
                            
                        </ul>

                        <h6 class="text-primary mt-3"><?php echo e(__('Step 4: Save & Use')); ?></h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Click "Create Template" to save')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('Template will be available for certificate generation')); ?></li>
                            <li><i class="ti ti-check text-success me-2"></i><?php echo e(__('You can edit or deactivate templates later')); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Template Information')); ?></h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('administration.certificate.template.store')); ?>" method="POST" id="templateForm">
                    <?php echo csrf_field(); ?>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label"><?php echo e(__('Template Title')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="<?php echo e(old('title')); ?>" required placeholder="<?php echo e(__('Enter template title')); ?>">
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label"><?php echo e(__('Description (Optional)')); ?></label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="<?php echo e(__('Enter template description')); ?>"><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="content" class="form-label"><?php echo e(__('Template Content')); ?> <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="15" required placeholder="<?php echo e(__('Enter certificate template content with placeholders')); ?>"><?php echo e(old('content')); ?></textarea>
                            <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="text-muted"><?php echo e(__('Use placeholders from the right panel to dynamically insert employee data')); ?></small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    <?php echo e(__('Active Template')); ?>

                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Create Template')); ?>

                            </button>
                            <a href="<?php echo e(route('administration.certificate.template.index')); ?>" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-1"></i><?php echo e(__('Back')); ?>

                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Available Placeholders')); ?></h6>
            </div>
            <div class="card-body">
                <p class="text-muted small"><?php echo e(__('Click on any placeholder to insert it into the template content')); ?></p>
                <div class="placeholder-list">
                    <?php $__currentLoopData = $placeholders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $placeholder => $description): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="placeholder-item" onclick="insertPlaceholder('<?php echo e($placeholder); ?>')">
                            <strong><?php echo e($placeholder); ?></strong><br>
                            <small class="text-muted"><?php echo e($description); ?></small>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Sample Template')); ?></h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong><?php echo e(__('Example Template:')); ?></strong><br><br>

                    <?php echo e(__('This is to certify that')); ?> <strong><?php echo e('{{'); ?>employee_name<?php echo e('); ?>'}}</strong> <?php echo e(__('has been employed with')); ?> <strong><?php echo e('{{'); ?>company_name<?php echo e('); ?>'}}</strong> <?php echo e(__('since')); ?> <strong><?php echo e('{{'); ?>joining_date<?php echo e('); ?>'}}</strong>.<br><br>

                    <?php echo e(__('During the employment period, the employee has shown excellent performance and dedication.')); ?><br><br>

                    <?php echo e(__('This certificate is issued on')); ?> <strong><?php echo e('{{'); ?>current_date<?php echo e('); ?>'}}</strong>.
                </small>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Certificate Types')); ?></h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-1">
                    <?php $__currentLoopData = $certificateTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="badge bg-light text-dark"><?php echo e($type); ?></span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_js'); ?>
    <script>
        function insertPlaceholder(placeholder) {
            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            textarea.value = text.substring(0, start) + placeholder + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
        }

        // Form validation
        document.getElementById('templateForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = document.getElementById('content').value.trim();

            if (!title) {
                e.preventDefault();
                alert('<?php echo e(__("Please enter a template title")); ?>');
                document.getElementById('title').focus();
                return;
            }

            if (!content) {
                e.preventDefault();
                alert('<?php echo e(__("Please enter template content")); ?>');
                document.getElementById('content').focus();
                return;
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/certificate/template/create.blade.php ENDPATH**/ ?>