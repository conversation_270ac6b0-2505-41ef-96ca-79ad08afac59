<?php

namespace App\Models\Certificate\Accessors;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait EmployeeCertificateAccessors
{
    /**
     * Get the formatted creation date
     */
    protected function formattedCreatedAt(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->created_at ? $this->created_at->format('M d, Y h:i A') : '';
            }
        );
    }

    /**
     * Get the file name from file path
     */
    protected function fileName(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->file_path ? basename($this->file_path) : '';
            }
        );
    }

    /**
     * Check if certificate has a PDF file
     */
    protected function hasPdf(): Attribute
    {
        return Attribute::make(
            get: function () {
                return !empty($this->file_path) && file_exists(storage_path('app/public/' . $this->file_path));
            }
        );
    }

    /**
     * Get a preview of the content (first 150 characters)
     */
    protected function contentPreview(): Attribute
    {
        return Attribute::make(
            get: function () {
                return strlen($this->content) > 150 
                    ? substr($this->content, 0, 150) . '...' 
                    : $this->content;
            }
        );
    }
}
