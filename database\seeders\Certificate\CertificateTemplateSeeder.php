<?php

namespace Database\Seeders\Certificate;

use Illuminate\Database\Seeder;
use App\Models\Certificate\CertificateTemplate;

class CertificateTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'title' => 'Employment Verification Letter',
                'description' => 'Standard employment verification letter template',
                'content' => 'TO WHOM IT MAY CONCERN

This is to certify that {{employee_name}} (Employee ID: {{employee_userid}}) has been employed with {{company_name}} since {{joining_date}}.

The employee is currently working in our organization and holds a position in our team. During the employment period, {{employee_first_name}} has shown excellent performance and dedication to work.

This certificate is issued upon the request of the employee for official purposes.

Issued on {{current_date}}.

Sincerely,
Human Resources Department
{{company_name}}',
                'is_active' => true,
                'creator_id' => 1, // Developer user
            ],
            [
                'title' => 'Experience Certificate',
                'description' => 'Experience certificate for employees leaving the organization',
                'content' => 'EXPERIENCE CERTIFICATE

This is to certify that {{employee_name}} (Employee ID: {{employee_userid}}) was employed with {{company_name}} from {{joining_date}} to {{current_date}}.

During the tenure with our organization, {{employee_first_name}} has worked with dedication and sincerity. {{employee_first_name}} has shown good professional conduct and has been a valuable member of our team.

We wish {{employee_first_name}} all the best for future endeavors.

This certificate is issued upon the request of the employee.

Issued on {{current_date}}.

Sincerely,
Human Resources Department
{{company_name}}',
                'is_active' => true,
                'creator_id' => 1,
            ],
            [
                'title' => 'Salary Certificate',
                'description' => 'Salary certificate template for official purposes',
                'content' => 'SALARY CERTIFICATE

TO WHOM IT MAY CONCERN

This is to certify that {{employee_name}} (Employee ID: {{employee_userid}}) is currently employed with {{company_name}} since {{joining_date}}.

The employee is a regular full-time employee of our organization and receives a monthly salary as per company policy.

This certificate is issued upon the request of the employee for official/bank purposes.

For any further verification, please feel free to contact our Human Resources Department.

Issued on {{current_date}}.

Sincerely,
Human Resources Department
{{company_name}}
Contact: {{employee_contact}}',
                'is_active' => true,
                'creator_id' => 1,
            ],
            [
                'title' => 'Appreciation Certificate',
                'description' => 'Certificate of appreciation for outstanding performance',
                'content' => 'CERTIFICATE OF APPRECIATION

This certificate is proudly presented to

{{employee_name}}

In recognition of outstanding performance and dedication to excellence at {{company_name}}.

Your hard work, commitment, and positive attitude have made a significant contribution to our organization\'s success.

We appreciate your valuable service and look forward to your continued excellence.

Presented on {{current_date}}.

With sincere appreciation,
Management Team
{{company_name}}',
                'is_active' => true,
                'creator_id' => 1,
            ],
            [
                'title' => 'NOC Letter',
                'description' => 'No Objection Certificate template',
                'content' => 'NO OBJECTION CERTIFICATE

TO WHOM IT MAY CONCERN

This is to certify that we have no objection if {{employee_name}} (Employee ID: {{employee_userid}}) pursues additional activities/studies/projects outside of regular working hours.

{{employee_first_name}} is a dedicated employee of {{company_name}} since {{joining_date}} and has been performing duties satisfactorily.

This NOC is issued upon the request of the employee for official purposes.

This certificate is valid until further notice.

Issued on {{current_date}}.

Sincerely,
Human Resources Department
{{company_name}}',
                'is_active' => true,
                'creator_id' => 1,
            ],
        ];

        foreach ($templates as $template) {
            CertificateTemplate::firstOrCreate(
                ['title' => $template['title']],
                $template
            );
        }
    }
}
