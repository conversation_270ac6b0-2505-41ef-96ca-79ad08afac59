<?php

namespace App\Http\Controllers\Administration\Certificate;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\User\Employee\Employee;
use App\Models\Certificate\CertificateTemplate;
use App\Models\Certificate\EmployeeCertificate;
use App\Services\Administration\Certificate\CertificateService;
use App\Http\Requests\Administration\Certificate\EmployeeCertificateStoreRequest;

class EmployeeCertificateController extends Controller
{
    protected $certificateService;

    public function __construct(CertificateService $certificateService)
    {
        $this->certificateService = $certificateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $userIds = auth()->user()->user_interactions->pluck('id');

        // Get employees for filtering
        $employees = Employee::with(['user'])
            ->whereHas('user', function ($query) use ($userIds) {
                $query->whereIn('id', $userIds)->whereStatus('Active');
            })
            ->get();

        $certificateTypes = CertificateTemplate::getCertificateTypes();
        $certificates = $this->certificateService->getCertificatesQuery($request)->get();

        return view('administration.certificate.index', compact('employees', 'certificateTypes', 'certificates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $userIds = auth()->user()->user_interactions->pluck('id');

        // Get employees for selection
        $employees = Employee::with(['user'])
            ->whereHas('user', function ($query) use ($userIds) {
                $query->whereIn('id', $userIds)->whereStatus('Active');
            })
            ->get();

        // Get active templates
        $templates = CertificateTemplate::where('is_active', true)
            ->orderBy('title')
            ->get();

        $certificateTypes = CertificateTemplate::getCertificateTypes();
        $placeholders = CertificateTemplate::getAvailablePlaceholders();

        return view('administration.certificate.create', compact('employees', 'templates', 'certificateTypes', 'placeholders'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(EmployeeCertificateStoreRequest $request)
    {
        /** @var \App\Models\Certificate\EmployeeCertificate|null $certificate */
        $certificate = null;

        try {
            DB::transaction(function () use ($request, &$certificate) {
                $certificate = $this->certificateService->generateCertificate($request->validated());

                // Generate PDF if requested
                if ($request->boolean('generate_pdf')) {
                    $this->certificateService->generatePDF($certificate);
                }
            });

            toast('Certificate Generated Successfully.', 'success');
            return redirect()->route('administration.certificate.show', ['certificate' => $certificate]);
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(EmployeeCertificate $certificate)
    {
        $certificate->load([
            'employee.user',
            'template',
            'creator',
            'files'
        ]);

        return view('administration.certificate.show', compact('certificate'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EmployeeCertificate $certificate)
    {
        try {
            $certificate->delete();

            toast('Certificate Deleted Successfully.', 'success');
            return redirect()->route('administration.certificate.index');
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }

    /**
     * Generate PDF for an existing certificate
     */
    public function generatePDF(EmployeeCertificate $certificate)
    {
        try {
            $this->certificateService->generatePDF($certificate);

            toast('Certificate PDF Generated Successfully.', 'success');
            return redirect()->back();
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }

    /**
     * Display PDF view for certificate
     */
    public function pdf($encryptedId)
    {
        try {
            $certificateId = decrypt($encryptedId);
            $certificate = EmployeeCertificate::with(['employee.user', 'template'])->findOrFail($certificateId);

            return view('administration.certificate.pdf', compact('certificate'));
        } catch (Exception $e) {
            abort(404, 'Certificate not found');
        }
    }

    /**
     * Preview certificate content before generating
     */
    public function preview(Request $request)
    {
        try {
            $employee = Employee::with('user')->findOrFail($request->employee_id);
            
            if ($request->filled('certificate_template_id')) {
                $template = CertificateTemplate::findOrFail($request->certificate_template_id);
                $content = $this->certificateService->processTemplate($template->content, $employee);
            } else {
                $content = $this->certificateService->processTemplate($request->content, $employee);
            }

            return response()->json([
                'success' => true,
                'content' => $content,
                'employee_name' => $employee->user->name
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get template content via AJAX
     */
    public function getTemplate(CertificateTemplate $template)
    {
        return response()->json([
            'success' => true,
            'content' => $template->content,
            'title' => $template->title
        ]);
    }
}
