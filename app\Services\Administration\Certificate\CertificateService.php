<?php

namespace App\Services\Administration\Certificate;

use Exception;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use App\Models\FileMedia\FileMedia;
use Spatie\Browsershot\Browsershot;
use App\Models\User\Employee\Employee;
use App\Models\Certificate\CertificateTemplate;
use App\Models\Certificate\EmployeeCertificate;

class CertificateService
{
    /**
     * Create a new certificate template
     */
    public function createTemplate(array $data): CertificateTemplate
    {
        return CertificateTemplate::create([
            'title' => $data['title'],
            'content' => $data['content'],
            'description' => $data['description'] ?? null,
            'is_active' => $data['is_active'] ?? true,
            'creator_id' => auth()->id(),
        ]);
    }

    /**
     * Update an existing certificate template
     */
    public function updateTemplate(CertificateTemplate $template, array $data): CertificateTemplate
    {
        $template->update([
            'title' => $data['title'],
            'content' => $data['content'],
            'description' => $data['description'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ]);

        return $template->fresh();
    }

    /**
     * Generate a certificate for an employee
     */
    public function generateCertificate(array $data): EmployeeCertificate
    {
        $employee = Employee::with('user')->findOrFail($data['employee_id']);
        
        // Get template if provided
        $template = null;
        if (!empty($data['certificate_template_id'])) {
            $template = CertificateTemplate::findOrFail($data['certificate_template_id']);
            $content = $this->processTemplate($template->content, $employee);
        } else {
            // Custom certificate content
            $content = $this->processTemplate($data['content'], $employee);
        }

        // Create the certificate record
        $certificate = EmployeeCertificate::create([
            'employee_id' => $data['employee_id'],
            'certificate_template_id' => $data['certificate_template_id'] ?? null,
            'certificate_type' => $data['certificate_type'],
            'content' => $content,
            'creator_id' => auth()->id(),
        ]);

        return $certificate;
    }

    /**
     * Process template content by replacing placeholders with employee data
     */
    public function processTemplate(string $content, Employee $employee): string
    {
        $placeholders = [
            '{{employee_name}}' => $employee->user->name,
            '{{employee_first_name}}' => $employee->user->first_name,
            '{{employee_last_name}}' => $employee->user->last_name,
            '{{employee_email}}' => $employee->user->email,
            '{{employee_userid}}' => $employee->user->userid,
            '{{employee_alias_name}}' => $employee->alias_name ?? $employee->user->name,
            '{{joining_date}}' => $employee->joining_date ? Carbon::parse($employee->joining_date)->format('F d, Y') : '',
            '{{current_date}}' => Carbon::now()->format('F d, Y'),
            '{{company_name}}' => config('app.name'),
            '{{company_address}}' => config('app.address', ''),
            '{{employee_contact}}' => $employee->official_contact_no ?? $employee->personal_contact_no ?? '',
            '{{employee_father_name}}' => $employee->father_name ?? '',
            '{{employee_mother_name}}' => $employee->mother_name ?? '',
            '{{employee_birth_date}}' => $employee->birth_date ? Carbon::parse($employee->birth_date)->format('F d, Y') : '',
            '{{employee_gender}}' => $employee->gender ?? '',
            '{{employee_blood_group}}' => $employee->blood_group ?? '',
        ];

        // Replace all placeholders
        foreach ($placeholders as $placeholder => $value) {
            $content = str_replace($placeholder, $value, $content);
        }

        return $content;
    }

    /**
     * Generate PDF for a certificate
     */
    public function generatePDF(EmployeeCertificate $certificate): FileMedia
    {
        // Generate the PDF content
        $pdfContent = $this->generateCertificatePDF($certificate);

        // Upload the generated PDF and return the FileMedia object
        return $this->uploadCertificatePDF($pdfContent, $certificate);
    }

    /**
     * Generate a PDF from the certificate content
     */
    private function generateCertificatePDF(EmployeeCertificate $certificate): string
    {
        $certificateId = encrypt($certificate->id);
        $url = route('administration.certificate.pdf', ['certificate' => $certificateId]);

        $nodeBinaryPath = config('browsershot.binary_path');
        $nodeMemorySize = config('browsershot.memory_size');

        return Browsershot::url($url)
            ->format('A4')
            ->setNodeBinary($nodeBinaryPath)
            ->setOption('args', ["--max-old-space-size={$nodeMemorySize}"])
            ->showBackground()
            ->pdf();
    }

    /**
     * Store the generated certificate PDF as a file
     */
    private function uploadCertificatePDF(string $pdfContent, EmployeeCertificate $certificate): FileMedia
    {
        $userId = $certificate->employee->user->userid;
        $certificateId = $certificate->id;
        $directory = "certificates/{$userId}";
        $fileName = "certificate_{$certificateId}.pdf";
        $tempFilePath = tempnam(sys_get_temp_dir(), 'certificate_') . '.pdf';
        file_put_contents($tempFilePath, $pdfContent);

        // Create a new UploadedFile instance
        $uploadedFile = new UploadedFile(
            $tempFilePath,
            $fileName,
            'application/pdf',
            null,
            true
        );

        // Use the store_file_media function to upload the PDF
        $fileMedia = store_file_media($uploadedFile, $certificate, $directory);

        // Update the certificate with the file path
        $certificate->update([
            'file_path' => $fileMedia->file_path
        ]);

        // Delete the temporary file
        unlink($tempFilePath);

        return $fileMedia;
    }

    /**
     * Get certificates with optional filtering
     */
    public function getCertificatesQuery($request = null)
    {
        $query = EmployeeCertificate::with(['employee.user', 'template', 'creator']);

        if ($request) {
            if ($request->filled('employee_id')) {
                $query->where('employee_id', $request->employee_id);
            }

            if ($request->filled('certificate_type')) {
                $query->where('certificate_type', $request->certificate_type);
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }
        }

        return $query->orderByDesc('created_at');
    }

    /**
     * Get templates with optional filtering
     */
    public function getTemplatesQuery($request = null)
    {
        $query = CertificateTemplate::with(['creator']);

        if ($request) {
            if ($request->filled('is_active')) {
                $query->where('is_active', $request->is_active);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }
        }

        return $query->orderByDesc('created_at');
    }
}
