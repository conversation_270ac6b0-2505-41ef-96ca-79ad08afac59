@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Certificate Templates'))

@section('css_links')
    {{--  External CSS  --}}
    <!-- DataTables css -->
    <link href="{{ asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/css/custom_css/datatables/datatable.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        /* Custom CSS Here */
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Certificate Templates') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('administration.dashboard.index') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('Certificate Templates') }}</li>
@endsection

@section('content')

<!-- Template Management Info -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="ti ti-info-circle me-2"></i>{{ __('Certificate Template Management') }}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="text-primary">{{ __('What are Certificate Templates?') }}</h6>
                        <p class="mb-3">{{ __('Certificate templates are pre-designed formats that can be reused to generate multiple certificates. They contain placeholders that are automatically replaced with employee-specific information.') }}</p>

                        <h6 class="text-primary">{{ __('Template Actions:') }}</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-eye text-info me-2"></i><strong>{{ __('View:') }}</strong> {{ __('See template details and usage history') }}</li>
                            <li><i class="ti ti-edit text-warning me-2"></i><strong>{{ __('Edit:') }}</strong> {{ __('Modify template content and settings') }}</li>
                            <li><i class="ti ti-toggle-right text-success me-2"></i><strong>{{ __('Activate/Deactivate:') }}</strong> {{ __('Control template availability') }}</li>
                            <li><i class="ti ti-trash text-danger me-2"></i><strong>{{ __('Delete:') }}</strong> {{ __('Permanently remove template') }}</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <a href="{{ route('administration.certificate.template.create') }}" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="ti ti-plus d-block mb-1" style="font-size: 2rem;"></i>
                                {{ __('Create New Template') }}
                            </a>
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-primary mb-2">{{ __('Quick Stats') }}</h6>
                                <p class="mb-1"><strong>{{ $templates->where('is_active', true)->count() }}</strong> {{ __('Active Templates') }}</p>
                                <p class="mb-0"><strong>{{ $templates->where('is_active', false)->count() }}</strong> {{ __('Inactive Templates') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Basic Bootstrap Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ __('Certificate Templates') }}</h5>
        @canany(['Certificate Everything', 'Certificate Create'])
            <a href="{{ route('administration.certificate.template.create') }}" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i>{{ __('Create Template') }}
            </a>
        @endcanany
    </div>

    <div class="card-body">
        <div class="table-responsive-md table-responsive-sm w-100">
            <table class="table data-table table-bordered">
                <thead>
                    <tr>
                        <th>{{ __('SL') }}</th>
                        <th>{{ __('Title') }}</th>
                        <th>{{ __('Description') }}</th>
                        <th>{{ __('Content Preview') }}</th>
                        <th>{{ __('Status') }}</th>
                        <th>{{ __('Created By') }}</th>
                        <th>{{ __('Created At') }}</th>
                        <th>{{ __('Action') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($templates as $key => $template)
                        <tr>
                            <td>{{ $key + 1 }}</td>
                            <td>
                                <strong>{{ $template->title }}</strong>
                            </td>
                            <td>
                                <small>{{ $template->description ?? __('No description') }}</small>
                            </td>
                            <td>
                                <small class="text-muted">{{ $template->content_preview }}</small>
                            </td>
                            <td>
                                <span class="{{ $template->status_badge_class }}">{{ $template->status_text }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs me-2">
                                        @if($template->creator->media->isNotEmpty())
                                            <img src="{{ $template->creator->media->first()->getUrl() }}" alt="Avatar" class="rounded-circle">
                                        @else
                                            <span class="avatar-initial rounded-circle bg-label-secondary">{{ substr($template->creator->name, 0, 1) }}</span>
                                        @endif
                                    </div>
                                    <small>{{ $template->creator->name }}</small>
                                </div>
                            </td>
                            <td>
                                <small>{{ $template->created_at->format('M d, Y h:i A') }}</small>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="ti ti-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        @canany(['Certificate Everything', 'Certificate Read'])
                                            <a class="dropdown-item" href="{{ route('administration.certificate.template.show', $template) }}">
                                                <i class="ti ti-eye me-1"></i>{{ __('View') }}
                                            </a>
                                        @endcanany
                                        @canany(['Certificate Everything', 'Certificate Update'])
                                            <a class="dropdown-item" href="{{ route('administration.certificate.template.edit', $template) }}">
                                                <i class="ti ti-edit me-1"></i>{{ __('Edit') }}
                                            </a>
                                            <a class="dropdown-item" href="javascript:void(0);" onclick="toggleStatus({{ $template->id }})">
                                                <i class="ti ti-toggle-{{ $template->is_active ? 'left' : 'right' }} me-1"></i>
                                                {{ $template->is_active ? __('Deactivate') : __('Activate') }}
                                            </a>
                                        @endcanany
                                        @canany(['Certificate Everything', 'Certificate Delete'])
                                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="confirmDelete({{ $template->id }})">
                                                <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                                            </a>
                                        @endcanany
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center">{{ __('No templates found') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

@endsection

@section('js_links')
    {{--  External JS  --}}
    <!-- DataTables js -->
    <script src="{{ asset('assets/js/custom_js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js') }}"></script>
@endsection

@section('custom_js')
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[ 6, "desc" ]]
            });
        });

        function toggleStatus(templateId) {
            if (confirm('{{ __("Are you sure you want to change the status of this template?") }}')) {
                $.ajax({
                    url: `{{ route('administration.certificate.template.toggle-status', '') }}/${templateId}`,
                    type: 'PATCH',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('{{ __("Error changing template status. Please try again.") }}');
                    }
                });
            }
        }

        function confirmDelete(templateId) {
            if (confirm('{{ __("Are you sure you want to delete this template? This action cannot be undone.") }}')) {
                $.ajax({
                    url: `{{ route('administration.certificate.template.destroy', '') }}/${templateId}`,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('{{ __("Error deleting template. Please try again.") }}');
                    }
                });
            }
        }
    </script>
@endsection
