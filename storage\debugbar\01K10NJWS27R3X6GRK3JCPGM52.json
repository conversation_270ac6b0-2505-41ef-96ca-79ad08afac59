{"__meta": {"id": "01K10NJWS27R3X6GRK3JCPGM52", "datetime": "2025-07-25 17:30:37", "utime": **********.989302, "method": "POST", "uri": "/settings/rolepermission/role/update/1", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753443035.248636, "end": **********.989335, "duration": 2.740699052810669, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1753443035.248636, "relative_start": 0, "end": **********.843651, "relative_end": **********.843651, "duration": 1.***************, "duration_str": "1.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.843709, "relative_start": 1.****************, "end": **********.989338, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.885787, "relative_start": 1.****************, "end": **********.90069, "relative_end": **********.90069, "duration": 0.014903068542480469, "duration_str": "14.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.979042, "relative_start": 2.****************, "end": **********.980594, "relative_end": **********.980594, "duration": 0.0015518665313720703, "duration_str": "1.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST settings/rolepermission/role/update/{role}", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Role Update", "controller": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@update<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=104\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.settings.rolepermission.role.update", "prefix": "settings/rolepermission/role", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=104\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Settings/Role/RoleController.php:104-128</a>"}, "queries": {"count": 139, "nb_statements": 137, "nb_visible_statements": 139, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.1636399999999999, "accumulated_duration_str": "164ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.02731, "duration": 0.018260000000000002, "duration_str": "18.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 11.159}, {"sql": "select * from `roles` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php", "line": 214}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.121161, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "blueorange", "explain": null, "start_percent": 11.159, "width_percent": 0.544}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.133665, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 11.703, "width_percent": 0.623}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.1444519, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 12.326, "width_percent": 0.471}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.148274, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 12.796, "width_percent": 0.483}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.164294, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 13.279, "width_percent": 0.764}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.171417, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 14.043, "width_percent": 0.519}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.196064, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 14.562, "width_percent": 1.094}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.357918, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "RoleController.php:109", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=109", "ajax": false, "filename": "RoleController.php", "line": "109"}, "connection": "blueorange", "explain": null, "start_percent": 15.656, "width_percent": 0}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'blueorange' and table_name = 'roles' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 110}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.35834, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:110", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=110", "ajax": false, "filename": "RoleController.php", "line": "110"}, "connection": "blueorange", "explain": null, "start_percent": 15.656, "width_percent": 3.52}, {"sql": "update `roles` set `roles`.`updated_at` = '2025-07-25 17:30:37' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-25 17:30:37", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 110}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.367192, "duration": 0.0062699999999999995, "duration_str": "6.27ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:110", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=110", "ajax": false, "filename": "RoleController.php", "line": "110"}, "connection": "blueorange", "explain": null, "start_percent": 19.176, "width_percent": 3.832}, {"sql": "select * from `permissions` where `id` in ('1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113', '114', '115', '116', '117', '118', '119', '120', '121', '122', '123')", "type": "query", "params": [], "bindings": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122", "123"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 117}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3794901, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:117", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=117", "ajax": false, "filename": "RoleController.php", "line": "117"}, "connection": "blueorange", "explain": null, "start_percent": 23.008, "width_percent": 1.894}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 407}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.397697, "duration": 0.01073, "duration_str": "10.73ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:407", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 407}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=407", "ajax": false, "filename": "HasPermissions.php", "line": "407"}, "connection": "blueorange", "explain": null, "start_percent": 24.902, "width_percent": 6.557}, {"sql": "select * from `role_has_permissions` where `role_has_permissions`.`role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4174392, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 31.459, "width_percent": 1.558}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.422297, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 33.018, "width_percent": 0.587}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (2, 1)", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.425919, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 33.604, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (3, 1)", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.429474, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 34.118, "width_percent": 0.532}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (4, 1)", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4328961, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 34.649, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (5, 1)", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.436348, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 35.169, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (6, 1)", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.439613, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 35.688, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (7, 1)", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.443133, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 36.214, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (8, 1)", "type": "query", "params": [], "bindings": [8, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.446295, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 36.709, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (9, 1)", "type": "query", "params": [], "bindings": [9, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.449552, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 37.204, "width_percent": 0.562}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (10, 1)", "type": "query", "params": [], "bindings": [10, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.453162, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 37.766, "width_percent": 0.556}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (11, 1)", "type": "query", "params": [], "bindings": [11, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.456539, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 38.322, "width_percent": 0.587}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (12, 1)", "type": "query", "params": [], "bindings": [12, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.459912, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 38.909, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (13, 1)", "type": "query", "params": [], "bindings": [13, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.463597, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 39.422, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (14, 1)", "type": "query", "params": [], "bindings": [14, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.467234, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 39.923, "width_percent": 0.764}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (15, 1)", "type": "query", "params": [], "bindings": [15, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.471277, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 40.687, "width_percent": 0.581}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (16, 1)", "type": "query", "params": [], "bindings": [16, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.47461, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 41.267, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (17, 1)", "type": "query", "params": [], "bindings": [17, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.477905, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 41.769, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (18, 1)", "type": "query", "params": [], "bindings": [18, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.481415, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 42.264, "width_percent": 0.648}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (19, 1)", "type": "query", "params": [], "bindings": [19, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.485439, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 42.911, "width_percent": 0.556}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (20, 1)", "type": "query", "params": [], "bindings": [20, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.489, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 43.467, "width_percent": 0.605}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (21, 1)", "type": "query", "params": [], "bindings": [21, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4926689, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 44.072, "width_percent": 0.464}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (22, 1)", "type": "query", "params": [], "bindings": [22, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.495846, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 44.537, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (23, 1)", "type": "query", "params": [], "bindings": [23, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.499302, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 45.05, "width_percent": 0.458}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (24, 1)", "type": "query", "params": [], "bindings": [24, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5029478, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 45.508, "width_percent": 0.581}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (25, 1)", "type": "query", "params": [], "bindings": [25, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5063128, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 46.089, "width_percent": 0.471}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (26, 1)", "type": "query", "params": [], "bindings": [26, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.509504, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 46.56, "width_percent": 0.44}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (27, 1)", "type": "query", "params": [], "bindings": [27, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.51264, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 47, "width_percent": 0.471}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (28, 1)", "type": "query", "params": [], "bindings": [28, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.515827, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 47.47, "width_percent": 0.477}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (29, 1)", "type": "query", "params": [], "bindings": [29, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.519369, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 47.947, "width_percent": 0.477}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (30, 1)", "type": "query", "params": [], "bindings": [30, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.52256, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 48.423, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (31, 1)", "type": "query", "params": [], "bindings": [31, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.525922, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 48.961, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (32, 1)", "type": "query", "params": [], "bindings": [32, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.529122, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 49.456, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (33, 1)", "type": "query", "params": [], "bindings": [33, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.532331, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 49.957, "width_percent": 0.617}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (34, 1)", "type": "query", "params": [], "bindings": [34, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.536219, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 50.574, "width_percent": 0.581}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (35, 1)", "type": "query", "params": [], "bindings": [35, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.539706, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 51.155, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (36, 1)", "type": "query", "params": [], "bindings": [36, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.543375, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 51.668, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (37, 1)", "type": "query", "params": [], "bindings": [37, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.546677, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 52.176, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (38, 1)", "type": "query", "params": [], "bindings": [38, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.549918, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 52.683, "width_percent": 0.464}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (39, 1)", "type": "query", "params": [], "bindings": [39, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5534801, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 53.147, "width_percent": 0.593}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (40, 1)", "type": "query", "params": [], "bindings": [40, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.556902, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 53.74, "width_percent": 0.483}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (41, 1)", "type": "query", "params": [], "bindings": [41, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5602, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 54.223, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (42, 1)", "type": "query", "params": [], "bindings": [42, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.563227, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 54.718, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (43, 1)", "type": "query", "params": [], "bindings": [43, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.566577, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 55.243, "width_percent": 0.532}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (44, 1)", "type": "query", "params": [], "bindings": [44, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.570181, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 55.775, "width_percent": 0.568}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (45, 1)", "type": "query", "params": [], "bindings": [45, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.573652, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 56.343, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (46, 1)", "type": "query", "params": [], "bindings": [46, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.576832, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 56.857, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (47, 1)", "type": "query", "params": [], "bindings": [47, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.580465, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 57.345, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (48, 1)", "type": "query", "params": [], "bindings": [48, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.584014, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 57.865, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (49, 1)", "type": "query", "params": [], "bindings": [49, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.587778, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 58.378, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (50, 1)", "type": "query", "params": [], "bindings": [50, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.591406, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 58.904, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (51, 1)", "type": "query", "params": [], "bindings": [51, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.595068, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 59.441, "width_percent": 0.532}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (52, 1)", "type": "query", "params": [], "bindings": [52, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.598813, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 59.973, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (53, 1)", "type": "query", "params": [], "bindings": [53, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6020238, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 60.486, "width_percent": 0.574}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (54, 1)", "type": "query", "params": [], "bindings": [54, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.605558, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 61.061, "width_percent": 0.574}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (55, 1)", "type": "query", "params": [], "bindings": [55, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.608866, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 61.635, "width_percent": 0.483}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (56, 1)", "type": "query", "params": [], "bindings": [56, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.612013, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 62.118, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (57, 1)", "type": "query", "params": [], "bindings": [57, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.615196, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 62.625, "width_percent": 0.477}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (58, 1)", "type": "query", "params": [], "bindings": [58, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.618629, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 63.102, "width_percent": 0.55}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (59, 1)", "type": "query", "params": [], "bindings": [59, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.622324, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 63.652, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (60, 1)", "type": "query", "params": [], "bindings": [60, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6259181, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 64.165, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (61, 1)", "type": "query", "params": [], "bindings": [61, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.629445, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 64.666, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (62, 1)", "type": "query", "params": [], "bindings": [62, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.633039, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 65.167, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (63, 1)", "type": "query", "params": [], "bindings": [63, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6366591, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 65.693, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (64, 1)", "type": "query", "params": [], "bindings": [64, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.639941, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 66.182, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (65, 1)", "type": "query", "params": [], "bindings": [65, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6434162, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 66.695, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (66, 1)", "type": "query", "params": [], "bindings": [66, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6465821, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 67.196, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (67, 1)", "type": "query", "params": [], "bindings": [67, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.649775, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 67.691, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (68, 1)", "type": "query", "params": [], "bindings": [68, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6534472, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 68.217, "width_percent": 0.599}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (69, 1)", "type": "query", "params": [], "bindings": [69, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.656976, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 68.816, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (70, 1)", "type": "query", "params": [], "bindings": [70, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.66067, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 69.329, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (71, 1)", "type": "query", "params": [], "bindings": [71, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.664325, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 69.842, "width_percent": 0.464}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (72, 1)", "type": "query", "params": [], "bindings": [72, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.667505, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 70.307, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (73, 1)", "type": "query", "params": [], "bindings": [73, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.671238, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 70.814, "width_percent": 0.568}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (74, 1)", "type": "query", "params": [], "bindings": [74, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6746662, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 71.382, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (75, 1)", "type": "query", "params": [], "bindings": [75, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.678046, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 71.896, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (76, 1)", "type": "query", "params": [], "bindings": [76, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.681483, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 72.415, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (77, 1)", "type": "query", "params": [], "bindings": [77, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.685318, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 72.941, "width_percent": 0.55}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (78, 1)", "type": "query", "params": [], "bindings": [78, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6889591, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 73.491, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (79, 1)", "type": "query", "params": [], "bindings": [79, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.692312, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 74.016, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (80, 1)", "type": "query", "params": [], "bindings": [80, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.695599, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 74.542, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (81, 1)", "type": "query", "params": [], "bindings": [81, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.698833, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 75.055, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (82, 1)", "type": "query", "params": [], "bindings": [82, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.702286, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 75.556, "width_percent": 0.715}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (83, 1)", "type": "query", "params": [], "bindings": [83, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7059639, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 76.271, "width_percent": 0.556}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (84, 1)", "type": "query", "params": [], "bindings": [84, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.709337, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 76.827, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (85, 1)", "type": "query", "params": [], "bindings": [85, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.712619, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 77.334, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (86, 1)", "type": "query", "params": [], "bindings": [86, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7158198, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 77.842, "width_percent": 0.471}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (87, 1)", "type": "query", "params": [], "bindings": [87, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.719228, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 78.312, "width_percent": 0.678}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (88, 1)", "type": "query", "params": [], "bindings": [88, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.723036, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 78.99, "width_percent": 0.55}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (89, 1)", "type": "query", "params": [], "bindings": [89, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.726289, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 79.54, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (90, 1)", "type": "query", "params": [], "bindings": [90, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.729458, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 80.054, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (91, 1)", "type": "query", "params": [], "bindings": [91, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7327302, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 80.579, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (92, 1)", "type": "query", "params": [], "bindings": [92, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.736065, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 81.074, "width_percent": 0.666}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (93, 1)", "type": "query", "params": [], "bindings": [93, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.739738, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 81.74, "width_percent": 0.532}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (94, 1)", "type": "query", "params": [], "bindings": [94, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.743051, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 82.272, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (95, 1)", "type": "query", "params": [], "bindings": [95, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.746175, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 82.761, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (96, 1)", "type": "query", "params": [], "bindings": [96, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.749264, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 83.25, "width_percent": 0.532}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (97, 1)", "type": "query", "params": [], "bindings": [97, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.752621, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 83.781, "width_percent": 0.562}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (98, 1)", "type": "query", "params": [], "bindings": [98, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.755939, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 84.344, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (99, 1)", "type": "query", "params": [], "bindings": [99, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.759106, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 84.863, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (100, 1)", "type": "query", "params": [], "bindings": [100, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.762198, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 85.401, "width_percent": 0.526}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (101, 1)", "type": "query", "params": [], "bindings": [101, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.76539, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 85.926, "width_percent": 0.544}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (102, 1)", "type": "query", "params": [], "bindings": [102, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.768743, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 86.47, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (103, 1)", "type": "query", "params": [], "bindings": [103, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.77226, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 86.984, "width_percent": 0.587}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (104, 1)", "type": "query", "params": [], "bindings": [104, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.775603, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 87.57, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (105, 1)", "type": "query", "params": [], "bindings": [105, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.779108, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 88.09, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (106, 1)", "type": "query", "params": [], "bindings": [106, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7824452, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 88.603, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (107, 1)", "type": "query", "params": [], "bindings": [107, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.786056, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 89.092, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (108, 1)", "type": "query", "params": [], "bindings": [108, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.78935, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 89.593, "width_percent": 0.519}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (109, 1)", "type": "query", "params": [], "bindings": [109, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.792799, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 90.112, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (110, 1)", "type": "query", "params": [], "bindings": [110, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7960632, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 90.614, "width_percent": 0.495}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (111, 1)", "type": "query", "params": [], "bindings": [111, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.799274, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 91.109, "width_percent": 0.501}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (112, 1)", "type": "query", "params": [], "bindings": [112, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8023791, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 91.61, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (113, 1)", "type": "query", "params": [], "bindings": [113, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.805816, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 92.123, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (114, 1)", "type": "query", "params": [], "bindings": [114, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.809443, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 92.661, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (115, 1)", "type": "query", "params": [], "bindings": [115, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8130481, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 93.174, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (116, 1)", "type": "query", "params": [], "bindings": [116, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8166702, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 93.687, "width_percent": 0.507}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (117, 1)", "type": "query", "params": [], "bindings": [117, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.820363, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 94.195, "width_percent": 0.629}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (118, 1)", "type": "query", "params": [], "bindings": [118, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.824087, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 94.824, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (119, 1)", "type": "query", "params": [], "bindings": [119, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.827264, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 95.362, "width_percent": 0.513}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (120, 1)", "type": "query", "params": [], "bindings": [120, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.830406, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 95.875, "width_percent": 0.538}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (121, 1)", "type": "query", "params": [], "bindings": [121, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.833606, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 96.413, "width_percent": 0.489}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (122, 1)", "type": "query", "params": [], "bindings": [122, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8368268, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 96.902, "width_percent": 0.483}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (123, 1)", "type": "query", "params": [], "bindings": [123, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.840148, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:376", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=376", "ajax": false, "filename": "HasPermissions.php", "line": "376"}, "connection": "blueorange", "explain": null, "start_percent": 97.385, "width_percent": 0.519}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 377}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 409}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 119}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8444092, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:377", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 377}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=377", "ajax": false, "filename": "HasPermissions.php", "line": "377"}, "connection": "blueorange", "explain": null, "start_percent": 97.904, "width_percent": 2.096}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.963817, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "RoleController.php:109", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=109", "ajax": false, "filename": "RoleController.php", "line": "109"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 246, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 253, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Role Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-467587008 data-indent-pad=\"  \"><span class=sf-dump-note>Role Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467587008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.207281, "xdebug_link": null}]}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/settings/rolepermission/role/edit/1\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752832853\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Role Has Been Updated.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/settings/rolepermission/role/update/1", "action_name": "administration.settings.rolepermission.role.update", "controller_action": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@update", "uri": "POST settings/rolepermission/role/update/{role}", "controller": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@update<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=104\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "settings/rolepermission/role", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=104\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Settings/Role/RoleController.php:104-128</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Role Update", "duration": "2.78s", "peak_memory": "32MB", "response": "Redirect to https://blueorange.test/settings/rolepermission/role/edit/1", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-376621962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-376621962\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-502578893 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>permissions</span>\" => <span class=sf-dump-note>array:123</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str>5</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str>6</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str>7</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str>8</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str>9</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"2 characters\">19</span>\"\n    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"2 characters\">26</span>\"\n    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n    <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n    <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"2 characters\">33</span>\"\n    <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n    <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"2 characters\">35</span>\"\n    <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"2 characters\">36</span>\"\n    <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"2 characters\">37</span>\"\n    <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"2 characters\">38</span>\"\n    <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"2 characters\">39</span>\"\n    <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n    <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"2 characters\">41</span>\"\n    <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"2 characters\">42</span>\"\n    <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n    <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n    <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n    <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"2 characters\">46</span>\"\n    <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n    <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"2 characters\">48</span>\"\n    <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"2 characters\">49</span>\"\n    <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"2 characters\">50</span>\"\n    <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"2 characters\">51</span>\"\n    <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"2 characters\">52</span>\"\n    <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n    <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"2 characters\">54</span>\"\n    <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n    <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n    <span class=sf-dump-index>56</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n    <span class=sf-dump-index>57</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n    <span class=sf-dump-index>58</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n    <span class=sf-dump-index>59</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n    <span class=sf-dump-index>60</span> => \"<span class=sf-dump-str title=\"2 characters\">61</span>\"\n    <span class=sf-dump-index>61</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n    <span class=sf-dump-index>62</span> => \"<span class=sf-dump-str title=\"2 characters\">63</span>\"\n    <span class=sf-dump-index>63</span> => \"<span class=sf-dump-str title=\"2 characters\">64</span>\"\n    <span class=sf-dump-index>64</span> => \"<span class=sf-dump-str title=\"2 characters\">65</span>\"\n    <span class=sf-dump-index>65</span> => \"<span class=sf-dump-str title=\"2 characters\">66</span>\"\n    <span class=sf-dump-index>66</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n    <span class=sf-dump-index>67</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n    <span class=sf-dump-index>68</span> => \"<span class=sf-dump-str title=\"2 characters\">69</span>\"\n    <span class=sf-dump-index>69</span> => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n    <span class=sf-dump-index>70</span> => \"<span class=sf-dump-str title=\"2 characters\">71</span>\"\n    <span class=sf-dump-index>71</span> => \"<span class=sf-dump-str title=\"2 characters\">72</span>\"\n    <span class=sf-dump-index>72</span> => \"<span class=sf-dump-str title=\"2 characters\">73</span>\"\n    <span class=sf-dump-index>73</span> => \"<span class=sf-dump-str title=\"2 characters\">74</span>\"\n    <span class=sf-dump-index>74</span> => \"<span class=sf-dump-str title=\"2 characters\">75</span>\"\n    <span class=sf-dump-index>75</span> => \"<span class=sf-dump-str title=\"2 characters\">76</span>\"\n    <span class=sf-dump-index>76</span> => \"<span class=sf-dump-str title=\"2 characters\">77</span>\"\n    <span class=sf-dump-index>77</span> => \"<span class=sf-dump-str title=\"2 characters\">78</span>\"\n    <span class=sf-dump-index>78</span> => \"<span class=sf-dump-str title=\"2 characters\">79</span>\"\n    <span class=sf-dump-index>79</span> => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n    <span class=sf-dump-index>80</span> => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n    <span class=sf-dump-index>81</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n    <span class=sf-dump-index>82</span> => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n    <span class=sf-dump-index>83</span> => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n    <span class=sf-dump-index>84</span> => \"<span class=sf-dump-str title=\"2 characters\">85</span>\"\n    <span class=sf-dump-index>85</span> => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n    <span class=sf-dump-index>86</span> => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n    <span class=sf-dump-index>87</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n    <span class=sf-dump-index>88</span> => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n    <span class=sf-dump-index>89</span> => \"<span class=sf-dump-str title=\"2 characters\">90</span>\"\n    <span class=sf-dump-index>90</span> => \"<span class=sf-dump-str title=\"2 characters\">91</span>\"\n    <span class=sf-dump-index>91</span> => \"<span class=sf-dump-str title=\"2 characters\">92</span>\"\n    <span class=sf-dump-index>92</span> => \"<span class=sf-dump-str title=\"2 characters\">93</span>\"\n    <span class=sf-dump-index>93</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n    <span class=sf-dump-index>94</span> => \"<span class=sf-dump-str title=\"2 characters\">95</span>\"\n    <span class=sf-dump-index>95</span> => \"<span class=sf-dump-str title=\"2 characters\">96</span>\"\n    <span class=sf-dump-index>96</span> => \"<span class=sf-dump-str title=\"2 characters\">97</span>\"\n    <span class=sf-dump-index>97</span> => \"<span class=sf-dump-str title=\"2 characters\">98</span>\"\n    <span class=sf-dump-index>98</span> => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n    <span class=sf-dump-index>99</span> => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n    <span class=sf-dump-index>100</span> => \"<span class=sf-dump-str title=\"3 characters\">101</span>\"\n    <span class=sf-dump-index>101</span> => \"<span class=sf-dump-str title=\"3 characters\">102</span>\"\n    <span class=sf-dump-index>102</span> => \"<span class=sf-dump-str title=\"3 characters\">103</span>\"\n    <span class=sf-dump-index>103</span> => \"<span class=sf-dump-str title=\"3 characters\">104</span>\"\n    <span class=sf-dump-index>104</span> => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n    <span class=sf-dump-index>105</span> => \"<span class=sf-dump-str title=\"3 characters\">106</span>\"\n    <span class=sf-dump-index>106</span> => \"<span class=sf-dump-str title=\"3 characters\">107</span>\"\n    <span class=sf-dump-index>107</span> => \"<span class=sf-dump-str title=\"3 characters\">108</span>\"\n    <span class=sf-dump-index>108</span> => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n    <span class=sf-dump-index>109</span> => \"<span class=sf-dump-str title=\"3 characters\">110</span>\"\n    <span class=sf-dump-index>110</span> => \"<span class=sf-dump-str title=\"3 characters\">111</span>\"\n    <span class=sf-dump-index>111</span> => \"<span class=sf-dump-str title=\"3 characters\">112</span>\"\n    <span class=sf-dump-index>112</span> => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n    <span class=sf-dump-index>113</span> => \"<span class=sf-dump-str title=\"3 characters\">114</span>\"\n    <span class=sf-dump-index>114</span> => \"<span class=sf-dump-str title=\"3 characters\">115</span>\"\n    <span class=sf-dump-index>115</span> => \"<span class=sf-dump-str title=\"3 characters\">116</span>\"\n    <span class=sf-dump-index>116</span> => \"<span class=sf-dump-str title=\"3 characters\">117</span>\"\n    <span class=sf-dump-index>117</span> => \"<span class=sf-dump-str title=\"3 characters\">118</span>\"\n    <span class=sf-dump-index>118</span> => \"<span class=sf-dump-str title=\"3 characters\">119</span>\"\n    <span class=sf-dump-index>119</span> => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n    <span class=sf-dump-index>120</span> => \"<span class=sf-dump-str title=\"3 characters\">121</span>\"\n    <span class=sf-dump-index>121</span> => \"<span class=sf-dump-str title=\"3 characters\">122</span>\"\n    <span class=sf-dump-index>122</span> => \"<span class=sf-dump-str title=\"3 characters\">123</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502578893\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1063325883 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2645</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Opera&quot;;v=&quot;120&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 OPR/120.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">https://blueorange.test/settings/rolepermission/role/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,bn;q=0.8,hi;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1080 characters\">bo_multi_tenant_session=eyJpdiI6IkNoNmFHTk5KRGpkVkE2NXZ1dGF3YVE9PSIsInZhbHVlIjoicTE4eGg3eFB0anNKRVZNUUx4Nzg5TWtQU0tpL2ovZEg3a0JiQVN4ck1FS3NSOFBWcTlZNHZlc084Qk9ZTlB0aFlYWmdCRkRvV1BNbmk2ZGJmbEhyOXVtT1RtMml4a3k5bDlmcFpNQ1I0SmtsdG1sSEpBMjMrZmVaTFlJN1BmQ1UiLCJtYWMiOiJjYzdlM2Y5NmQ5MWY5ODM4YjU0ZWY2Y2IxOTZmZDY2ZGNjNzExMzliN2Q4MjcyYzY2MDVhM2JhOGQ4ZmRiMzk2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImliN2VKckphY2RSRVF2b3pFOXdqOEE9PSIsInZhbHVlIjoiNDF2UzZqV0xDTlcvYUVtTk9vYTNTRit5cFF1K1R3T1pKd0RyVXY2YnpLU2pOWU5wSnJPSHM4WnZxTkxrTm43R2t4dTRmRk1LV2RHLzI4WGtFWXVjby9nOVVOWTVsOUkyQWJyaldOWDZEWTdqRlFLYjdRQ1JjV0gyblBWNzVreCsiLCJtYWMiOiIzOTQ3N2Y4YmEyYTQyMTExMTYwOGUxMWI0YmYyYWNhMjFhNjVlYWU0YTc2YWQ0YTVjNmI0MWUzNGZjNGE2OWYxIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlAvUTFBY3h2OU1lMXQ5Qm5HL0NLMmc9PSIsInZhbHVlIjoiT3Z3RHNHc00vcXlYTHRKeXhnbm13TTRacWl3d0VtY283TDRCSFpEVHduL0lFQ0dqTzc0UHJNZmVGWHd2K3NGUWlBcVFaUjZWQmp3T1luaUFaRUdydXZkOGpraTJRenZyY0UwQXFaSVFZNUlpckNRTVRJcEhKbFJhR29sdVlzbmYiLCJtYWMiOiIyN2M1MTk5NTdjMjJjNDY3NDczZmM5ZmNjNGQ5OTI3OGFjODdhNDM3ODJmNTkxYmU0ZGMxM2JhZjA4NTg4NmMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063325883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1284112246 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bo_multi_tenant_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU9hKUo8erm4hsoGsDajoymGto9RQabaIruS2lk5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284112246\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-411986956 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 11:30:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">https://blueorange.test/settings/rolepermission/role/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411986956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1526372600 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://blueorange.test/settings/rolepermission/role/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752832853</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"434 characters\">{&quot;title&quot;:&quot;Role Has Been Updated.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526372600\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/settings/rolepermission/role/update/1", "action_name": "administration.settings.rolepermission.role.update", "controller_action": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@update"}, "badge": "302 Found"}}