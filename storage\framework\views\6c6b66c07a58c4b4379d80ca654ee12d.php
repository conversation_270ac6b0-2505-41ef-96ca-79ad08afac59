<!-- Category Create Modal -->
<div class="modal fade" data-bs-backdrop="static" id="assignNewCategoryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Assign Category</h3>
                    <p class="text-muted">Assign A New Category</p>
                </div>
                <!-- Category Create form -->
                <form method="post" action="<?php echo e(route('administration.accounts.income_expense.category.store')); ?>" class="row g-3" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <div class="col-md-8">
                        <label class="form-label">Category Name <strong class="text-danger">*</strong></label>
                        <input type="text" name="name" value="<?php echo e(old('name')); ?>" class="form-control" placeholder="Ex: Electronics" tabindex="-1" required/>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-4">
                        <label for="is_active" class="form-label"><?php echo e(__('Select Status')); ?></label>
                        <select name="is_active" id="is_active" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                            <option value=""><?php echo e(__('Select Type')); ?></option>
                            <option value="<?php echo e(TRUE); ?>" selected><?php echo e(__('Active')); ?></option>
                            <option value="<?php echo e(FALSE); ?>"><?php echo e(__('Inactive')); ?></option>
                        </select>
                        <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-12">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3" placeholder="Ex: Electronical Products."><?php echo e(old('description')); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-12 text-center mt-4">
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary me-sm-3 me-1">
                            <i class="ti ti-check"></i>
                            Create Category
                        </button>
                    </div>
                </form>
                <!--/ Category Create form -->
            </div>
        </div>
    </div>
</div>
<!--/ Category Create Modal --><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/accounts/income_expense/category/modals/category_create.blade.php ENDPATH**/ ?>