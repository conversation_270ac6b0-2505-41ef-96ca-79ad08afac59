<?php

namespace App\Http\Controllers\Administration\Certificate;

use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\Certificate\CertificateTemplate;
use App\Services\Administration\Certificate\CertificateService;
use App\Http\Requests\Administration\Certificate\CertificateTemplateStoreRequest;
use App\Http\Requests\Administration\Certificate\CertificateTemplateUpdateRequest;

class CertificateTemplateController extends Controller
{
    protected $certificateService;

    public function __construct(CertificateService $certificateService)
    {
        $this->certificateService = $certificateService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $templates = $this->certificateService->getTemplatesQuery($request)->get();

        return view('administration.certificate.template.index', compact('templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $certificateTypes = CertificateTemplate::getCertificateTypes();
        $placeholders = CertificateTemplate::getAvailablePlaceholders();

        return view('administration.certificate.template.create', compact('certificateTypes', 'placeholders'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CertificateTemplateStoreRequest $request)
    {
        /** @var \App\Models\Certificate\CertificateTemplate|null $template */
        $template = null;

        try {
            DB::transaction(function () use ($request, &$template) {
                $template = $this->certificateService->createTemplate($request->validated());
            });

            toast('Certificate Template Created Successfully.', 'success');
            return redirect()->route('administration.certificate.template.show', ['template' => $template]);
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CertificateTemplate $template)
    {
        $template->load(['creator', 'certificates.employee.user']);

        return view('administration.certificate.template.show', compact('template'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CertificateTemplate $template)
    {
        $certificateTypes = CertificateTemplate::getCertificateTypes();
        $placeholders = CertificateTemplate::getAvailablePlaceholders();

        return view('administration.certificate.template.edit', compact('template', 'certificateTypes', 'placeholders'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CertificateTemplateUpdateRequest $request, CertificateTemplate $template)
    {
        try {
            DB::transaction(function () use ($request, $template) {
                $this->certificateService->updateTemplate($template, $request->validated());
            });

            toast('Certificate Template Updated Successfully.', 'success');
            return redirect()->route('administration.certificate.template.show', ['template' => $template]);
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CertificateTemplate $template)
    {
        try {
            $template->delete();

            toast('Certificate Template Deleted Successfully.', 'success');
            return redirect()->route('administration.certificate.template.index');
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }

    /**
     * Toggle the active status of a template
     */
    public function toggleStatus(CertificateTemplate $template)
    {
        try {
            $template->update(['is_active' => !$template->is_active]);

            $status = $template->is_active ? 'activated' : 'deactivated';
            toast("Certificate Template {$status} successfully.", 'success');

            return redirect()->back();
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }
}
