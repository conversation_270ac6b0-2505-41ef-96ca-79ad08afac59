<?php

namespace App\Models\Certificate\Relations;

use App\Models\User;
use App\Models\Certificate\EmployeeCertificate;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait CertificateTemplateRelations
{
    /**
     * Get the user who created this template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get all certificates generated using this template.
     */
    public function certificates(): HasMany
    {
        return $this->hasMany(EmployeeCertificate::class, 'certificate_template_id');
    }
}
