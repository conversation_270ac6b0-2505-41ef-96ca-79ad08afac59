<!DOCTYPE html>

<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="light-style layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-theme="theme-default" data-assets-path="/assets/" data-template="">
    <head>
        
        <?php echo $__env->make('layouts.administration.partials.metas', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        
        
        <title><?php echo e(config('app.name')); ?> || Not Found</title>
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset(config('app.favicon'))); ?>" />

        <!-- Start css -->
        <?php echo $__env->make('layouts.administration.partials.stylesheet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End css -->

        <!-- Page CSS -->
        <!-- Page -->
        <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/css/pages/page-misc.css')); ?>" />
    </head>

    <body>
        <!-- Content -->

        <!-- Not Authorized -->
        <div class="container">
            <div class="misc-wrapper">
                <h4 class="mb-0 mx-2 text-primary text-bold"><?php echo e($exception->getStatusCode()); ?> Error!</h4>
                <h2 class="mb-1 mx-2">Oops! 😖 The requested URL was not found on this server.</h2>
                <?php if(auth()->guard()->check()): ?>
                    
                        <p class="mb-4 mx-2"><?php echo e($exception->getMessage()); ?></p>
                    
                <?php endif; ?>
                <a href="<?php echo e(url()->previous()); ?>" class="btn btn-primary mb-4">
                    <i class="ti ti-arrow-left" style="font-size: 20px; margin-top: -2px; padding-right: 4px;"></i>
                    Back to Previous Page
                </a>
                <div class="mt-0">
                    <img src="<?php echo e(asset('assets/img/illustrations/page-misc-error.png')); ?>" alt="page-misc-not-authorized" width="200" class="img-fluid" />
                </div>
            </div>
        </div>
        <!-- /Not Authorized -->
    </body>
</html>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/errors/404.blade.php ENDPATH**/ ?>