<?php

namespace App\Models\Certificate\Mutators;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait EmployeeCertificateMutators
{
    /**
     * Set the certificate type with proper formatting
     */
    protected function certificateType(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return trim($value);
            }
        );
    }

    /**
     * Set the content with proper formatting
     */
    protected function content(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return trim($value);
            }
        );
    }

    /**
     * Set the file path with proper formatting
     */
    protected function filePath(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return $value ? trim($value) : null;
            }
        );
    }
}
