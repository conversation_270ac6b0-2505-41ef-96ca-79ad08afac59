<?php

namespace App\Http\Requests\Administration\Certificate;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CertificateTemplateUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->can('Certificate Update');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required', 
                'string', 
                'max:255', 
                Rule::unique('certificate_templates', 'title')->ignore($this->template->id)
            ],
            'content' => ['required', 'string'],
            'description' => ['nullable', 'string', 'max:1000'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The certificate template title is required.',
            'title.unique' => 'A certificate template with this title already exists.',
            'content.required' => 'The certificate template content is required.',
            'description.max' => 'The description may not be greater than 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'title' => 'template title',
            'content' => 'template content',
            'description' => 'template description',
            'is_active' => 'active status',
        ];
    }
}
