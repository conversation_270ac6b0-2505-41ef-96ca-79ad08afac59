<!-- Certificate Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create', 'Certificate Read', 'Certificate Update', 'Certificate Delete'])): ?>
    <li class="menu-item <?php echo e(request()->is('certificate*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-certificate"></i>
            <div data-i18n="Certificate"><?php echo e(__('Certificate')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('certificate/all*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.certificate.index')); ?>" class="menu-link"><?php echo e(__('All Certificates')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create'])): ?>
                <li class="menu-item <?php echo e(request()->is('certificate/create*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.certificate.create')); ?>" class="menu-link"><?php echo e(__('Generate Certificate')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Read', 'Certificate Update', 'Certificate Delete'])): ?>
                <li class="menu-item <?php echo e(request()->is('certificate/template*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Templates"><?php echo e(__('Templates')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Read'])): ?>
                            <li class="menu-item <?php echo e(request()->is('certificate/template/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.certificate.template.index')); ?>" class="menu-link">
                                    <div data-i18n="All Templates"><?php echo e(__('All Templates')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create'])): ?>
                            <li class="menu-item <?php echo e(request()->is('certificate/template/create*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.certificate.template.create')); ?>" class="menu-link">
                                    <div data-i18n="Create Template"><?php echo e(__('Create Template')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/certificate.blade.php ENDPATH**/ ?>