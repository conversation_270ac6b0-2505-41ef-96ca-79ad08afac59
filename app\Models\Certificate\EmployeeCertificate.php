<?php

namespace App\Models\Certificate;

use App\Traits\HasCustomRouteId;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use App\Models\Certificate\Relations\EmployeeCertificateRelations;
use App\Models\Certificate\Accessors\EmployeeCertificateAccessors;
use App\Models\Certificate\Mutators\EmployeeCertificateMutators;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmployeeCertificate extends Model
{
    use HasFactory, SoftDeletes, CascadeSoftDeletes, HasCustomRouteId;

    // Relations
    use EmployeeCertificateRelations;

    // Accessors & Mutators
    use EmployeeCertificateAccessors, EmployeeCertificateMutators;

    protected $cascadeDeletes = [];

    protected $fillable = [
        'employee_id',
        'certificate_template_id',
        'certificate_type',
        'content',
        'file_path',
        'creator_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
