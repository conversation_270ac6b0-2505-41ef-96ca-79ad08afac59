<?php

namespace App\Models\Certificate;

use App\Traits\HasCustomRouteId;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use App\Models\Certificate\Relations\CertificateTemplateRelations;
use App\Models\Certificate\Accessors\CertificateTemplateAccessors;
use App\Models\Certificate\Mutators\CertificateTemplateMutators;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CertificateTemplate extends Model
{
    use HasFactory, SoftDeletes, CascadeSoftDeletes, HasCustomRouteId;

    // Relations
    use CertificateTemplateRelations;

    // Accessors & Mutators
    use CertificateTemplateAccessors, CertificateTemplateMutators;

    protected $cascadeDeletes = [];

    protected $fillable = [
        'title',
        'content',
        'description',
        'is_active',
        'creator_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get available certificate types
     */
    public static function getCertificateTypes(): array
    {
        return [
            'Appointment Letter',
            'Employment Verification Letter',
            'Experience Certificate',
            'Salary Certificate',
            'Appreciation Certificate',
            'Award Certificate',
            'Internship Completion Certificate',
            'NOC Letter',
            'Visa Letter',
            'Custom Certificate',
        ];
    }

    /**
     * Get available placeholders for templates
     */
    public static function getAvailablePlaceholders(): array
    {
        return [
            '{{employee_name}}' => 'Employee Full Name',
            '{{employee_first_name}}' => 'Employee First Name',
            '{{employee_last_name}}' => 'Employee Last Name',
            '{{employee_email}}' => 'Employee Email',
            '{{employee_userid}}' => 'Employee User ID',
            '{{employee_alias_name}}' => 'Employee Alias Name',
            '{{employee_designation}}' => 'Employee Designation',
            '{{employee_department}}' => 'Employee Department',
            '{{joining_date}}' => 'Joining Date',
            '{{current_date}}' => 'Current Date',
            '{{company_name}}' => 'Company Name',
            '{{company_address}}' => 'Company Address',
            '{{employee_salary}}' => 'Employee Salary',
            '{{employee_contact}}' => 'Employee Contact Number',
            '{{employee_father_name}}' => 'Employee Father Name',
            '{{employee_mother_name}}' => 'Employee Mother Name',
            '{{employee_birth_date}}' => 'Employee Birth Date',
            '{{employee_gender}}' => 'Employee Gender',
            '{{employee_blood_group}}' => 'Employee Blood Group',
        ];
    }
}
