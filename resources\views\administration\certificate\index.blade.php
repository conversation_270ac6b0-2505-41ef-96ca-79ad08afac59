@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('All Certificates'))

@section('css_links')
    {{--  External CSS  --}}
    <!-- DataTables css -->
    <link href="{{ asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/css/custom_css/datatables/datatable.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        /* Custom CSS Here */
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('All Certificates') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('administration.dashboard.index') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('All Certificates') }}</li>
@endsection

@section('content')

<!-- Quick Actions Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="ti ti-rocket me-2"></i>{{ __('Certificate Management Quick Actions') }}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <a href="{{ route('administration.certificate.create') }}" class="btn btn-outline-primary btn-lg w-100 mb-2">
                                <i class="ti ti-plus d-block mb-1" style="font-size: 2rem;"></i>
                                {{ __('Generate New Certificate') }}
                            </a>
                            <small class="text-muted">{{ __('Create certificates for employees') }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <a href="{{ route('administration.certificate.template.create') }}" class="btn btn-outline-info btn-lg w-100 mb-2">
                                <i class="ti ti-template d-block mb-1" style="font-size: 2rem;"></i>
                                {{ __('Create Template') }}
                            </a>
                            <small class="text-muted">{{ __('Design reusable certificate templates') }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <a href="{{ route('administration.certificate.template.index') }}" class="btn btn-outline-warning btn-lg w-100 mb-2">
                                <i class="ti ti-list d-block mb-1" style="font-size: 2rem;"></i>
                                {{ __('Manage Templates') }}
                            </a>
                            <small class="text-muted">{{ __('View and edit existing templates') }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="btn btn-outline-secondary btn-lg w-100 mb-2 disabled">
                                <i class="ti ti-chart-bar d-block mb-1" style="font-size: 2rem;"></i>
                                {{ __('Total Certificates') }}
                            </div>
                            <small class="text-muted">{{ $certificates->count() }} {{ __('certificates generated') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Basic Bootstrap Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ __('All Certificates') }}</h5>
        @canany(['Certificate Everything', 'Certificate Create'])
            <a href="{{ route('administration.certificate.create') }}" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i>{{ __('Generate Certificate') }}
            </a>
        @endcanany
    </div>

    <div class="card-body">
        <div class="table-responsive-md table-responsive-sm w-100">
            <table class="table data-table table-bordered">
                <thead>
                    <tr>
                        <th>{{ __('SL') }}</th>
                        <th>{{ __('Employee') }}</th>
                        <th>{{ __('Certificate Type') }}</th>
                        <th>{{ __('Template') }}</th>
                        <th>{{ __('Generated By') }}</th>
                        <th>{{ __('Generated At') }}</th>
                        <th>{{ __('PDF') }}</th>
                        <th>{{ __('Action') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($certificates as $key => $certificate)
                        <tr>
                            <td>{{ $key + 1 }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        @if($certificate->employee->user->media->isNotEmpty())
                                            <img src="{{ $certificate->employee->user->media->first()->getUrl() }}" alt="Avatar" class="rounded-circle">
                                        @else
                                            <span class="avatar-initial rounded-circle bg-label-primary">{{ substr($certificate->employee->user->name, 0, 1) }}</span>
                                        @endif
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $certificate->employee->user->name }}</h6>
                                        <small class="text-muted">{{ $certificate->employee->user->userid }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $certificate->certificate_type }}</span>
                            </td>
                            <td>
                                @if($certificate->template)
                                    <a href="{{ route('administration.certificate.template.show', $certificate->template) }}" class="text-primary">
                                        {{ $certificate->template->title }}
                                    </a>
                                @else
                                    <span class="text-muted">{{ __('Custom Certificate') }}</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs me-2">
                                        @if($certificate->creator->media->isNotEmpty())
                                            <img src="{{ $certificate->creator->media->first()->getUrl() }}" alt="Avatar" class="rounded-circle">
                                        @else
                                            <span class="avatar-initial rounded-circle bg-label-secondary">{{ substr($certificate->creator->name, 0, 1) }}</span>
                                        @endif
                                    </div>
                                    <small>{{ $certificate->creator->name }}</small>
                                </div>
                            </td>
                            <td>
                                <small>{{ $certificate->formatted_created_at }}</small>
                            </td>
                            <td>
                                @if($certificate->has_pdf)
                                    <a href="{{ route('administration.certificate.pdf', encrypt($certificate->id)) }}" target="_blank" class="btn btn-sm btn-outline-success">
                                        <i class="ti ti-file-type-pdf me-1"></i>{{ __('View PDF') }}
                                    </a>
                                @else
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="generatePDF({{ $certificate->id }})">
                                        <i class="ti ti-file-plus me-1"></i>{{ __('Generate PDF') }}
                                    </button>
                                @endif
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="ti ti-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        @canany(['Certificate Everything', 'Certificate Read'])
                                            <a class="dropdown-item" href="{{ route('administration.certificate.show', $certificate) }}">
                                                <i class="ti ti-eye me-1"></i>{{ __('View') }}
                                            </a>
                                        @endcanany
                                        @if(!$certificate->has_pdf)
                                            @canany(['Certificate Everything', 'Certificate Create'])
                                                <a class="dropdown-item" href="javascript:void(0);" onclick="generatePDF({{ $certificate->id }})">
                                                    <i class="ti ti-file-plus me-1"></i>{{ __('Generate PDF') }}
                                                </a>
                                            @endcanany
                                        @endif
                                        @canany(['Certificate Everything', 'Certificate Delete'])
                                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="confirmDelete({{ $certificate->id }})">
                                                <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                                            </a>
                                        @endcanany
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center">{{ __('No certificates found') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

@endsection

@section('js_links')
    {{--  External JS  --}}
    <!-- DataTables js -->
    <script src="{{ asset('assets/js/custom_js/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js') }}"></script>
@endsection

@section('custom_js')
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[ 5, "desc" ]]
            });
        });

        function generatePDF(certificateId) {
            if (confirm('{{ __("Are you sure you want to generate PDF for this certificate?") }}')) {
                $.ajax({
                    url: `{{ route('administration.certificate.generate-pdf', '') }}/${certificateId}`,
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('{{ __("Error generating PDF. Please try again.") }}');
                    }
                });
            }
        }

        function confirmDelete(certificateId) {
            if (confirm('{{ __("Are you sure you want to delete this certificate?") }}')) {
                $.ajax({
                    url: `{{ route('administration.certificate.destroy', '') }}/${certificateId}`,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('{{ __("Error deleting certificate. Please try again.") }}');
                    }
                });
            }
        }
    </script>
@endsection
