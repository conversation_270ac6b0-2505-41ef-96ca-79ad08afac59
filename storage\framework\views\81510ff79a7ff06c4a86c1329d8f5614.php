<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('All Certificates')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Certificates')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('All Certificates')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Basic Bootstrap Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><?php echo e(__('All Certificates')); ?></h5>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create'])): ?>
            <a href="<?php echo e(route('administration.certificate.create')); ?>" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i><?php echo e(__('Generate Certificate')); ?>

            </a>
        <?php endif; ?>
    </div>

    <div class="card-body">
        <div class="table-responsive-md table-responsive-sm w-100">
            <table class="table data-table table-bordered">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Employee')); ?></th>
                        <th><?php echo e(__('Certificate Type')); ?></th>
                        <th><?php echo e(__('Template')); ?></th>
                        <th><?php echo e(__('Generated By')); ?></th>
                        <th><?php echo e(__('Generated At')); ?></th>
                        <th><?php echo e(__('PDF')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $certificates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($key + 1); ?></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        <?php if($certificate->employee->user->media->isNotEmpty()): ?>
                                            <img src="<?php echo e($certificate->employee->user->media->first()->getUrl()); ?>" alt="Avatar" class="rounded-circle">
                                        <?php else: ?>
                                            <span class="avatar-initial rounded-circle bg-label-primary"><?php echo e(substr($certificate->employee->user->name, 0, 1)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($certificate->employee->user->name); ?></h6>
                                        <small class="text-muted"><?php echo e($certificate->employee->user->userid); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($certificate->certificate_type); ?></span>
                            </td>
                            <td>
                                <?php if($certificate->template): ?>
                                    <a href="<?php echo e(route('administration.certificate.template.show', $certificate->template)); ?>" class="text-primary">
                                        <?php echo e($certificate->template->title); ?>

                                    </a>
                                <?php else: ?>
                                    <span class="text-muted"><?php echo e(__('Custom Certificate')); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs me-2">
                                        <?php if($certificate->creator->media->isNotEmpty()): ?>
                                            <img src="<?php echo e($certificate->creator->media->first()->getUrl()); ?>" alt="Avatar" class="rounded-circle">
                                        <?php else: ?>
                                            <span class="avatar-initial rounded-circle bg-label-secondary"><?php echo e(substr($certificate->creator->name, 0, 1)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <small><?php echo e($certificate->creator->name); ?></small>
                                </div>
                            </td>
                            <td>
                                <small><?php echo e($certificate->formatted_created_at); ?></small>
                            </td>
                            <td>
                                <?php if($certificate->has_pdf): ?>
                                    <a href="<?php echo e(route('administration.certificate.pdf', encrypt($certificate->id))); ?>" target="_blank" class="btn btn-sm btn-outline-success">
                                        <i class="ti ti-file-type-pdf me-1"></i><?php echo e(__('View PDF')); ?>

                                    </a>
                                <?php else: ?>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="generatePDF(<?php echo e($certificate->id); ?>)">
                                        <i class="ti ti-file-plus me-1"></i><?php echo e(__('Generate PDF')); ?>

                                    </button>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="ti ti-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Read'])): ?>
                                            <a class="dropdown-item" href="<?php echo e(route('administration.certificate.show', $certificate)); ?>">
                                                <i class="ti ti-eye me-1"></i><?php echo e(__('View')); ?>

                                            </a>
                                        <?php endif; ?>
                                        <?php if(!$certificate->has_pdf): ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create'])): ?>
                                                <a class="dropdown-item" href="javascript:void(0);" onclick="generatePDF(<?php echo e($certificate->id); ?>)">
                                                    <i class="ti ti-file-plus me-1"></i><?php echo e(__('Generate PDF')); ?>

                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Delete'])): ?>
                                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="confirmDelete(<?php echo e($certificate->id); ?>)">
                                                <i class="ti ti-trash me-1"></i><?php echo e(__('Delete')); ?>

                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo e(__('No certificates found')); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_links'); ?>
    
    <!-- DataTables js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_js'); ?>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[ 5, "desc" ]]
            });
        });

        function generatePDF(certificateId) {
            if (confirm('<?php echo e(__("Are you sure you want to generate PDF for this certificate?")); ?>')) {
                $.ajax({
                    url: `<?php echo e(route('administration.certificate.generate-pdf', '')); ?>/${certificateId}`,
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('<?php echo e(__("Error generating PDF. Please try again.")); ?>');
                    }
                });
            }
        }

        function confirmDelete(certificateId) {
            if (confirm('<?php echo e(__("Are you sure you want to delete this certificate?")); ?>')) {
                $.ajax({
                    url: `<?php echo e(route('administration.certificate.destroy', '')); ?>/${certificateId}`,
                    type: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('<?php echo e(__("Error deleting certificate. Please try again.")); ?>');
                    }
                });
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/certificate/index.blade.php ENDPATH**/ ?>