@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Certificate Details'))

@section('css_links')
    {{--  External CSS  --}}
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        .certificate-content {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            line-height: 1.8;
        }
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Certificate Details') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('administration.dashboard.index') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item">
        <a href="{{ route('administration.certificate.index') }}">{{ __('All Certificates') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('Certificate Details') }}</li>
@endsection

@section('content')

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Certificate Information') }}</h5>
                <div>
                    @if($certificate->has_pdf)
                        <a href="{{ route('administration.certificate.pdf', encrypt($certificate->id)) }}" target="_blank" class="btn btn-success btn-sm">
                            <i class="ti ti-file-type-pdf me-1"></i>{{ __('View PDF') }}
                        </a>
                    @else
                        @canany(['Certificate Everything', 'Certificate Create'])
                            <button type="button" class="btn btn-primary btn-sm" onclick="generatePDF()">
                                <i class="ti ti-file-plus me-1"></i>{{ __('Generate PDF') }}
                            </button>
                        @endcanany
                    @endif
                    @canany(['Certificate Everything', 'Certificate Delete'])
                        <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete()">
                            <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                        </button>
                    @endcanany
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>{{ __('Employee Information') }}</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="avatar avatar-sm me-3">
                                @if($certificate->employee->user->media->isNotEmpty())
                                    <img src="{{ $certificate->employee->user->media->first()->getUrl() }}" alt="Avatar" class="rounded-circle">
                                @else
                                    <span class="avatar-initial rounded-circle bg-label-primary">{{ substr($certificate->employee->user->name, 0, 1) }}</span>
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $certificate->employee->user->name }}</h6>
                                <small class="text-muted">{{ $certificate->employee->user->userid }}</small>
                            </div>
                        </div>
                        <p class="mb-1"><strong>{{ __('Email') }}:</strong> {{ $certificate->employee->user->email }}</p>
                        <p class="mb-1"><strong>{{ __('Joining Date') }}:</strong> {{ $certificate->employee->joining_date ? \Carbon\Carbon::parse($certificate->employee->joining_date)->format('F d, Y') : 'N/A' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>{{ __('Certificate Information') }}</h6>
                        <p class="mb-1"><strong>{{ __('Type') }}:</strong> <span class="badge bg-info">{{ $certificate->certificate_type }}</span></p>
                        <p class="mb-1"><strong>{{ __('Generated At') }}:</strong> {{ $certificate->formatted_created_at }}</p>
                        <p class="mb-1"><strong>{{ __('Template Used') }}:</strong> 
                            @if($certificate->template)
                                <a href="{{ route('administration.certificate.template.show', $certificate->template) }}" class="text-primary">
                                    {{ $certificate->template->title }}
                                </a>
                            @else
                                <span class="text-muted">{{ __('Custom Certificate') }}</span>
                            @endif
                        </p>
                        <p class="mb-1"><strong>{{ __('Generated By') }}:</strong> {{ $certificate->creator->name }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h6>{{ __('Certificate Content') }}</h6>
                        <div class="certificate-content">
                            {!! nl2br(e($certificate->content)) !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Actions') }}</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($certificate->has_pdf)
                        <a href="{{ route('administration.certificate.pdf', encrypt($certificate->id)) }}" target="_blank" class="btn btn-outline-success">
                            <i class="ti ti-file-type-pdf me-1"></i>{{ __('View PDF') }}
                        </a>
                        <a href="{{ route('administration.certificate.pdf', encrypt($certificate->id)) }}" download class="btn btn-outline-info">
                            <i class="ti ti-download me-1"></i>{{ __('Download PDF') }}
                        </a>
                    @else
                        @canany(['Certificate Everything', 'Certificate Create'])
                            <button type="button" class="btn btn-outline-primary" onclick="generatePDF()">
                                <i class="ti ti-file-plus me-1"></i>{{ __('Generate PDF') }}
                            </button>
                        @endcanany
                    @endif
                    
                    <a href="{{ route('administration.certificate.create') }}" class="btn btn-outline-secondary">
                        <i class="ti ti-plus me-1"></i>{{ __('Generate New Certificate') }}
                    </a>
                    
                    <a href="{{ route('administration.certificate.index') }}" class="btn btn-outline-dark">
                        <i class="ti ti-arrow-left me-1"></i>{{ __('Back to List') }}
                    </a>
                </div>
            </div>
        </div>

        @if($certificate->files->isNotEmpty())
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">{{ __('Attached Files') }}</h6>
                </div>
                <div class="card-body">
                    @foreach($certificate->files as $file)
                        <div class="d-flex align-items-center mb-2">
                            <i class="ti ti-file-type-pdf text-danger me-2"></i>
                            <div class="flex-grow-1">
                                <a href="{{ $file->getUrl() }}" target="_blank" class="text-decoration-none">
                                    {{ $file->original_name }}
                                </a>
                                <br>
                                <small class="text-muted">{{ $file->human_readable_size }}</small>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>

@endsection

@section('js_links')
    {{--  External JS  --}}
@endsection

@section('custom_js')
    <script>
        function generatePDF() {
            if (confirm('{{ __("Are you sure you want to generate PDF for this certificate?") }}')) {
                $.ajax({
                    url: '{{ route('administration.certificate.generate-pdf', $certificate) }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('{{ __("Error generating PDF. Please try again.") }}');
                    }
                });
            }
        }

        function confirmDelete() {
            if (confirm('{{ __("Are you sure you want to delete this certificate? This action cannot be undone.") }}')) {
                $.ajax({
                    url: '{{ route('administration.certificate.destroy', $certificate) }}',
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        window.location.href = '{{ route('administration.certificate.index') }}';
                    },
                    error: function(xhr) {
                        alert('{{ __("Error deleting certificate. Please try again.") }}');
                    }
                });
            }
        }
    </script>
@endsection
