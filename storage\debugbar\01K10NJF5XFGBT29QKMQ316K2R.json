{"__meta": {"id": "01K10NJF5XFGBT29QKMQ316K2R", "datetime": "2025-07-25 17:30:24", "utime": **********.063622, "method": "GET", "uri": "/settings/rolepermission/role/all", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753443020.607359, "end": **********.063654, "duration": 3.4562950134277344, "duration_str": "3.46s", "measures": [{"label": "Booting", "start": 1753443020.607359, "relative_start": 0, "end": **********.492175, "relative_end": **********.492175, "duration": 1.****************, "duration_str": "1.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492202, "relative_start": 1.***************, "end": **********.063659, "relative_end": 5.0067901611328125e-06, "duration": 1.****************, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.55447, "relative_start": 1.****************, "end": **********.613414, "relative_end": **********.613414, "duration": 0.058943986892700195, "duration_str": "58.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.180861, "relative_start": 2.****************, "end": **********.055495, "relative_end": **********.055495, "duration": 0.****************, "duration_str": "875ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 27, "nb_templates": 27, "templates": [{"name": "1x administration.settings.role.index", "param_count": null, "params": [], "start": **********.191284, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.phpadministration.settings.role.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fadministration%2Fsettings%2Frole%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "administration.settings.role.index"}, {"name": "1x layouts.administration.app", "param_count": null, "params": [], "start": **********.749683, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/app.blade.phplayouts.administration.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.app"}, {"name": "1x layouts.administration.partials.metas", "param_count": null, "params": [], "start": **********.7514, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/metas.blade.phplayouts.administration.partials.metas", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmetas.blade.php&line=1", "ajax": false, "filename": "metas.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.metas"}, {"name": "1x layouts.administration.partials.stylesheet", "param_count": null, "params": [], "start": **********.752909, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/stylesheet.blade.phplayouts.administration.partials.stylesheet", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fstylesheet.blade.php&line=1", "ajax": false, "filename": "stylesheet.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.stylesheet"}, {"name": "1x layouts.administration.partials.sidenav", "param_count": null, "params": [], "start": **********.754815, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/sidenav.blade.phplayouts.administration.partials.sidenav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fsidenav.blade.php&line=1", "ajax": false, "filename": "sidenav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.sidenav"}, {"name": "1x layouts.administration.partials.menus.dashboard", "param_count": null, "params": [], "start": **********.757582, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/dashboard.blade.phplayouts.administration.partials.menus.dashboard", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.dashboard"}, {"name": "1x layouts.administration.partials.menus.chatting", "param_count": null, "params": [], "start": **********.759452, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/chatting.blade.phplayouts.administration.partials.menus.chatting", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fchatting.blade.php&line=1", "ajax": false, "filename": "chatting.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.chatting"}, {"name": "1x layouts.administration.partials.menus.attendance", "param_count": null, "params": [], "start": **********.774962, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/attendance.blade.phplayouts.administration.partials.menus.attendance", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fattendance.blade.php&line=1", "ajax": false, "filename": "attendance.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.attendance"}, {"name": "1x layouts.administration.partials.menus.daily_break", "param_count": null, "params": [], "start": **********.790573, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_break.blade.phplayouts.administration.partials.menus.daily_break", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_break.blade.php&line=1", "ajax": false, "filename": "daily_break.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_break"}, {"name": "1x layouts.administration.partials.menus.daily_work_update", "param_count": null, "params": [], "start": **********.799367, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/daily_work_update.blade.phplayouts.administration.partials.menus.daily_work_update", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fdaily_work_update.blade.php&line=1", "ajax": false, "filename": "daily_work_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.daily_work_update"}, {"name": "1x layouts.administration.partials.menus.task", "param_count": null, "params": [], "start": **********.814251, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/task.blade.phplayouts.administration.partials.menus.task", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Ftask.blade.php&line=1", "ajax": false, "filename": "task.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.task"}, {"name": "1x layouts.administration.partials.menus.leave", "param_count": null, "params": [], "start": **********.826099, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/leave.blade.phplayouts.administration.partials.menus.leave", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fleave.blade.php&line=1", "ajax": false, "filename": "leave.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.leave"}, {"name": "1x layouts.administration.partials.menus.announcement", "param_count": null, "params": [], "start": **********.838751, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/announcement.blade.phplayouts.administration.partials.menus.announcement", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fannouncement.blade.php&line=1", "ajax": false, "filename": "announcement.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.announcement"}, {"name": "1x layouts.administration.partials.menus.it_ticket", "param_count": null, "params": [], "start": **********.848443, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/it_ticket.blade.phplayouts.administration.partials.menus.it_ticket", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fit_ticket.blade.php&line=1", "ajax": false, "filename": "it_ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.it_ticket"}, {"name": "1x layouts.administration.partials.menus.booking", "param_count": null, "params": [], "start": **********.859821, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/booking.blade.phplayouts.administration.partials.menus.booking", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fbooking.blade.php&line=1", "ajax": false, "filename": "booking.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.booking"}, {"name": "1x layouts.administration.partials.menus.penalty", "param_count": null, "params": [], "start": **********.868289, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/penalty.blade.phplayouts.administration.partials.menus.penalty", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fpenalty.blade.php&line=1", "ajax": false, "filename": "penalty.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.penalty"}, {"name": "1x layouts.administration.partials.menus.quiz", "param_count": null, "params": [], "start": **********.884401, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/quiz.blade.phplayouts.administration.partials.menus.quiz", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fquiz.blade.php&line=1", "ajax": false, "filename": "quiz.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.quiz"}, {"name": "1x layouts.administration.partials.menus.certificate", "param_count": null, "params": [], "start": **********.922653, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/certificate.blade.phplayouts.administration.partials.menus.certificate", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fcertificate.blade.php&line=1", "ajax": false, "filename": "certificate.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.certificate"}, {"name": "1x layouts.administration.partials.menus.vault", "param_count": null, "params": [], "start": **********.937848, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/vault.blade.phplayouts.administration.partials.menus.vault", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fvault.blade.php&line=1", "ajax": false, "filename": "vault.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.vault"}, {"name": "1x layouts.administration.partials.menus.settings", "param_count": null, "params": [], "start": **********.945422, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/settings.blade.phplayouts.administration.partials.menus.settings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsettings.blade.php&line=1", "ajax": false, "filename": "settings.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.settings"}, {"name": "1x layouts.administration.partials.menus.salary", "param_count": null, "params": [], "start": **********.984666, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/salary.blade.phplayouts.administration.partials.menus.salary", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fsalary.blade.php&line=1", "ajax": false, "filename": "salary.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.salary"}, {"name": "1x layouts.administration.partials.menus.income_expense", "param_count": null, "params": [], "start": **********.989013, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/income_expense.blade.phplayouts.administration.partials.menus.income_expense", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Fincome_expense.blade.php&line=1", "ajax": false, "filename": "income_expense.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.income_expense"}, {"name": "1x layouts.administration.partials.menus.logs", "param_count": null, "params": [], "start": **********.006438, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/menus/logs.blade.phplayouts.administration.partials.menus.logs", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fmenus%2Flogs.blade.php&line=1", "ajax": false, "filename": "logs.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.menus.logs"}, {"name": "1x layouts.administration.partials.topnav", "param_count": null, "params": [], "start": **********.012368, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.phplayouts.administration.partials.topnav", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=1", "ajax": false, "filename": "topnav.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.topnav"}, {"name": "1x layouts.administration.partials.breadcrumb", "param_count": null, "params": [], "start": **********.047236, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/breadcrumb.blade.phplayouts.administration.partials.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.breadcrumb"}, {"name": "1x layouts.administration.partials.scripts", "param_count": null, "params": [], "start": **********.051149, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/scripts.blade.phplayouts.administration.partials.scripts", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.administration.partials.scripts"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.053076, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET settings/rolepermission/role/all", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Role Read", "controller": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.settings.rolepermission.role.index", "prefix": "settings/rolepermission/role", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Settings/Role/RoleController.php:20-34</a>"}, "queries": {"count": 120, "nb_statements": 120, "nb_visible_statements": 120, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.1644800000000001, "accumulated_duration_str": "164ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.890822, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 4.396}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.913342, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 4.396, "width_percent": 0.468}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.925279, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 4.864, "width_percent": 0.438}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.928617, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 5.302, "width_percent": 0.474}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.947427, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 5.776, "width_percent": 0.669}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.954858, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 6.445, "width_percent": 0.499}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9807131, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 6.943, "width_percent": 0.924}, {"sql": "select `roles`.*, (select count(*) from `users` inner join `model_has_roles` on `users`.`id` = `model_has_roles`.`model_id` where `roles`.`id` = `model_has_roles`.`role_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `status` = 'Active' and `users`.`deleted_at` is null) as `active_users_count` from `roles`", "type": "query", "params": [], "bindings": ["App\\Models\\User", "Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9968219, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:31", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=31", "ajax": false, "filename": "RoleController.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 7.867, "width_percent": 2.566}, {"sql": "select `users`.*, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `users` inner join `model_has_roles` on `users`.`id` = `model_has_roles`.`model_id` where `model_has_roles`.`role_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `status` = 'Active' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", "Active"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.00763, "duration": 0.0058, "duration_str": "5.8ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:31", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=31", "ajax": false, "filename": "RoleController.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 10.433, "width_percent": 3.526}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.029852, "duration": 0.010230000000000001, "duration_str": "10.23ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:31", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Administration/Settings/Role/RoleController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=31", "ajax": false, "filename": "RoleController.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 13.959, "width_percent": 6.22}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.2099628, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 20.179, "width_percent": 1.854}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.217741, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 22.033, "width_percent": 1.052}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.272569, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 23.085, "width_percent": 0.632}, {"sql": "select * from `employees` where `employees`.`user_id` = 2 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.278539, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 23.717, "width_percent": 0.754}, {"sql": "select * from `media` where `media`.`model_id` in (2) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.2819371, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 24.471, "width_percent": 0.59}, {"sql": "select * from `employees` where `employees`.`user_id` = 3 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.2860959, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 25.061, "width_percent": 1.222}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.290642, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 26.283, "width_percent": 0.663}, {"sql": "select * from `users` where `users`.`id` = 3 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.2963922, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 26.946, "width_percent": 0.59}, {"sql": "select * from `employees` where `employees`.`user_id` = 12 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.300748, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 27.535, "width_percent": 0.73}, {"sql": "select * from `media` where `media`.`model_id` in (12) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.304851, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 28.265, "width_percent": 0.657}, {"sql": "select * from `users` where `users`.`id` = 12 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.31073, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 28.921, "width_percent": 0.626}, {"sql": "select * from `employees` where `employees`.`user_id` = 17 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.314774, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 29.548, "width_percent": 0.669}, {"sql": "select * from `media` where `media`.`model_id` in (17) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.318914, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 30.216, "width_percent": 1.046}, {"sql": "select * from `users` where `users`.`id` = 17 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.325323, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 31.262, "width_percent": 0.596}, {"sql": "select * from `employees` where `employees`.`user_id` = 19 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.328987, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 31.858, "width_percent": 0.693}, {"sql": "select * from `media` where `media`.`model_id` in (19) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.332722, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 32.551, "width_percent": 0.717}, {"sql": "select * from `users` where `users`.`id` = 19 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.33909, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 33.268, "width_percent": 0.62}, {"sql": "select * from `employees` where `employees`.`user_id` = 21 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.343105, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 33.889, "width_percent": 0.742}, {"sql": "select * from `media` where `media`.`model_id` in (21) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.346641, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 34.63, "width_percent": 0.705}, {"sql": "select * from `users` where `users`.`id` = 21 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.3523731, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 35.336, "width_percent": 0.821}, {"sql": "select * from `employees` where `employees`.`user_id` = 25 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.3568969, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 36.156, "width_percent": 0.766}, {"sql": "select * from `media` where `media`.`model_id` in (25) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.360425, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 36.922, "width_percent": 0.596}, {"sql": "select * from `users` where `users`.`id` = 25 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.3658879, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 37.518, "width_percent": 0.565}, {"sql": "select * from `employees` where `employees`.`user_id` = 13 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.370563, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 38.084, "width_percent": 0.894}, {"sql": "select * from `media` where `media`.`model_id` in (13) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.3746269, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 38.977, "width_percent": 0.675}, {"sql": "select * from `users` where `users`.`id` = 13 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.380341, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 39.652, "width_percent": 0.602}, {"sql": "select * from `employees` where `employees`.`user_id` = 22 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.384214, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 40.254, "width_percent": 0.772}, {"sql": "select * from `media` where `media`.`model_id` in (22) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.388037, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 41.026, "width_percent": 0.784}, {"sql": "select * from `users` where `users`.`id` = 22 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.3939939, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 41.811, "width_percent": 0.578}, {"sql": "select * from `employees` where `employees`.`user_id` = 45 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.3980548, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 42.388, "width_percent": 0.681}, {"sql": "select * from `media` where `media`.`model_id` in (45) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.40166, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 43.069, "width_percent": 0.717}, {"sql": "select * from `users` where `users`.`id` = 45 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.4076169, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 43.786, "width_percent": 0.584}, {"sql": "select * from `employees` where `employees`.`user_id` = 10 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.411709, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 44.37, "width_percent": 0.717}, {"sql": "select * from `media` where `media`.`model_id` in (10) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.415254, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 45.088, "width_percent": 0.626}, {"sql": "select * from `users` where `users`.`id` = 10 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.421402, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 45.714, "width_percent": 0.815}, {"sql": "select * from `employees` where `employees`.`user_id` = 14 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.425771, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 46.528, "width_percent": 0.705}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.4294431, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 47.234, "width_percent": 0.699}, {"sql": "select * from `users` where `users`.`id` = 14 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.435128, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 47.933, "width_percent": 0.626}, {"sql": "select * from `employees` where `employees`.`user_id` = 18 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.439843, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 48.559, "width_percent": 0.687}, {"sql": "select * from `media` where `media`.`model_id` in (18) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.443515, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 49.246, "width_percent": 0.62}, {"sql": "select * from `users` where `users`.`id` = 18 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.4488711, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 49.866, "width_percent": 0.59}, {"sql": "select * from `employees` where `employees`.`user_id` = 11 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.453279, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 50.456, "width_percent": 1.082}, {"sql": "select * from `media` where `media`.`model_id` in (11) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.457685, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 51.538, "width_percent": 0.644}, {"sql": "select * from `users` where `users`.`id` = 11 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.463277, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 52.183, "width_percent": 0.602}, {"sql": "select * from `employees` where `employees`.`user_id` = 23 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.46772, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 52.785, "width_percent": 0.754}, {"sql": "select * from `media` where `media`.`model_id` in (23) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.472032, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 53.538, "width_percent": 0.736}, {"sql": "select * from `users` where `users`.`id` = 23 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.477251, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 54.274, "width_percent": 0.553}, {"sql": "select * from `employees` where `employees`.`user_id` = 5 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.481468, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 54.827, "width_percent": 0.73}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.485444, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 55.557, "width_percent": 0.845}, {"sql": "select * from `users` where `users`.`id` = 5 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.492198, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 56.402, "width_percent": 0.699}, {"sql": "select * from `employees` where `employees`.`user_id` = 4 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.497159, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 57.101, "width_percent": 0.748}, {"sql": "select * from `media` where `media`.`model_id` in (4) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.501057, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 57.849, "width_percent": 0.669}, {"sql": "select * from `users` where `users`.`id` = 4 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.507334, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 58.518, "width_percent": 0.571}, {"sql": "select * from `employees` where `employees`.`user_id` = 6 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.511262, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 59.089, "width_percent": 0.675}, {"sql": "select * from `media` where `media`.`model_id` in (6) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.514899, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 59.764, "width_percent": 0.699}, {"sql": "select * from `users` where `users`.`id` = 6 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.520611, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 60.463, "width_percent": 0.73}, {"sql": "select * from `employees` where `employees`.`user_id` = 7 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.524925, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 61.193, "width_percent": 0.669}, {"sql": "select * from `media` where `media`.`model_id` in (7) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.52849, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 61.862, "width_percent": 0.711}, {"sql": "select * from `users` where `users`.`id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.5342672, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 62.573, "width_percent": 0.596}, {"sql": "select * from `employees` where `employees`.`user_id` = 70 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.539038, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 63.169, "width_percent": 0.894}, {"sql": "select * from `media` where `media`.`model_id` in (70) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.542916, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 64.063, "width_percent": 0.711}, {"sql": "select * from `users` where `users`.`id` = 70 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [70], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.548473, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 64.774, "width_percent": 0.584}, {"sql": "select * from `employees` where `employees`.`user_id` = 76 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [76], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.552692, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 65.357, "width_percent": 0.839}, {"sql": "select * from `media` where `media`.`model_id` in (76) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.557337, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 66.196, "width_percent": 0.839}, {"sql": "select * from `users` where `users`.`id` = 76 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [76], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.563346, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 67.036, "width_percent": 0.638}, {"sql": "select * from `employees` where `employees`.`user_id` = 77 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.567257, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 67.674, "width_percent": 0.681}, {"sql": "select * from `media` where `media`.`model_id` in (77) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.571532, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 68.355, "width_percent": 0.803}, {"sql": "select * from `users` where `users`.`id` = 77 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.577862, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 69.157, "width_percent": 0.614}, {"sql": "select * from `employees` where `employees`.`user_id` = 8 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.5822551, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 69.771, "width_percent": 0.699}, {"sql": "select * from `media` where `media`.`model_id` in (8) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.585739, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 70.471, "width_percent": 0.687}, {"sql": "select * from `users` where `users`.`id` = 8 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.591512, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 71.158, "width_percent": 0.626}, {"sql": "select * from `employees` where `employees`.`user_id` = 15 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.595849, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 71.784, "width_percent": 0.742}, {"sql": "select * from `media` where `media`.`model_id` in (15) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.599331, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 72.526, "width_percent": 0.742}, {"sql": "select * from `users` where `users`.`id` = 15 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.605408, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 73.267, "width_percent": 0.778}, {"sql": "select * from `employees` where `employees`.`user_id` = 20 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.609757, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 74.045, "width_percent": 0.663}, {"sql": "select * from `media` where `media`.`model_id` in (20) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.613208, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 74.708, "width_percent": 0.663}, {"sql": "select * from `users` where `users`.`id` = 20 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.618624, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 75.371, "width_percent": 0.559}, {"sql": "select * from `employees` where `employees`.`user_id` = 9 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6242738, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 75.93, "width_percent": 0.687}, {"sql": "select * from `media` where `media`.`model_id` in (9) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.627757, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 76.617, "width_percent": 0.912}, {"sql": "select * from `users` where `users`.`id` = 9 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.63431, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 77.529, "width_percent": 0.596}, {"sql": "select * from `employees` where `employees`.`user_id` = 16 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.638451, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 78.125, "width_percent": 0.827}, {"sql": "select * from `media` where `media`.`model_id` in (16) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.642623, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 78.952, "width_percent": 0.723}, {"sql": "select * from `users` where `users`.`id` = 16 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.6483068, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 79.675, "width_percent": 0.578}, {"sql": "select * from `employees` where `employees`.`user_id` = 93 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6528459, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 80.253, "width_percent": 0.681}, {"sql": "select * from `media` where `media`.`model_id` in (93) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.6569228, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 80.934, "width_percent": 0.681}, {"sql": "select * from `users` where `users`.`id` = 93 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.6628509, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 81.615, "width_percent": 0.681}, {"sql": "select * from `employees` where `employees`.`user_id` = 95 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.666923, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 82.296, "width_percent": 0.669}, {"sql": "select * from `media` where `media`.`model_id` in (95) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.6704931, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 82.964, "width_percent": 0.827}, {"sql": "select * from `users` where `users`.`id` = 95 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.6766489, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 83.791, "width_percent": 0.559}, {"sql": "select * from `employees` where `employees`.`user_id` = 96 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.680575, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 84.351, "width_percent": 0.675}, {"sql": "select * from `media` where `media`.`model_id` in (96) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.684146, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 85.026, "width_percent": 0.632}, {"sql": "select * from `users` where `users`.`id` = 96 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.689643, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 85.658, "width_percent": 0.796}, {"sql": "select * from `employees` where `employees`.`user_id` = 97 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [97], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.693889, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 86.454, "width_percent": 0.669}, {"sql": "select * from `media` where `media`.`model_id` in (97) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.697537, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 87.123, "width_percent": 0.73}, {"sql": "select * from `users` where `users`.`id` = 97 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [97], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.702956, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 87.853, "width_percent": 0.584}, {"sql": "select * from `employees` where `employees`.`user_id` = 122 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.707026, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 88.436, "width_percent": 0.845}, {"sql": "select * from `media` where `media`.`model_id` in (122) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.71086, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 89.281, "width_percent": 0.657}, {"sql": "select * from `users` where `users`.`id` = 122 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.7165618, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 89.938, "width_percent": 0.584}, {"sql": "select * from `employees` where `employees`.`user_id` = 121 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [121], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.7210789, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 90.522, "width_percent": 0.784}, {"sql": "select * from `media` where `media`.`model_id` in (121) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.7251399, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 91.306, "width_percent": 0.681}, {"sql": "select * from `users` where `users`.`id` = 121 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [121], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.730756, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 91.987, "width_percent": 0.608}, {"sql": "select * from `employees` where `employees`.`user_id` = 111 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 59}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.735187, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:56", "source": {"index": 21, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=56", "ajax": false, "filename": "UserAccessors.php", "line": "56"}, "connection": "blueorange", "explain": null, "start_percent": 92.595, "width_percent": 0.736}, {"sql": "select * from `media` where `media`.`model_id` in (111) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "administration.settings.role.index", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/administration/settings/role/index.blade.php", "line": 60}], "start": **********.739212, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 93.33, "width_percent": 0.827}, {"sql": "select * from `users` where `users`.`id` = 111 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.745332, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 94.157, "width_percent": 0.608}, {"sql": "select count(*) as aggregate from `chattings` where `receiver_id` = 1 and `seen_at` is null and `chattings`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.762673, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ChattingHelper.php:61", "source": {"index": 16, "namespace": null, "name": "app/Helpers/ChattingHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\ChattingHelper.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FChattingHelper.php&line=61", "ajax": false, "filename": "ChattingHelper.php", "line": "61"}, "connection": "blueorange", "explain": null, "start_percent": 94.765, "width_percent": 0.632}, {"sql": "select * from `shortcuts` where `shortcuts`.`user_id` = 1 and `shortcuts`.`user_id` is not null and `shortcuts`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.018045, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:67", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=67", "ajax": false, "filename": "topnav.blade.php", "line": "67"}, "connection": "blueorange", "explain": null, "start_percent": 95.398, "width_percent": 0.754}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.023263, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:88", "source": {"index": 20, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=88", "ajax": false, "filename": "topnav.blade.php", "line": "88"}, "connection": "blueorange", "explain": null, "start_percent": 96.152, "width_percent": 1.593}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 259}, {"index": 27, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 158}], "start": **********.029524, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "blueorange", "explain": null, "start_percent": 97.744, "width_percent": 0.699}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/BaseUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\BaseUrlGenerator.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/Support/UrlGenerator/DefaultUrlGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\Support\\UrlGenerator\\DefaultUrlGenerator.php", "line": 12}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/Models/Media.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\Models\\Media.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 304}], "start": **********.035624, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "UserPathGenerator.php:16", "source": {"index": 21, "namespace": null, "name": "app/Services/MediaLibrary/PathGenerators/UserPathGenerator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\MediaLibrary\\PathGenerators\\UserPathGenerator.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FMediaLibrary%2FPathGenerators%2FUserPathGenerator.php&line=16", "ajax": false, "filename": "UserPathGenerator.php", "line": "16"}, "connection": "blueorange", "explain": null, "start_percent": 98.444, "width_percent": 0.608}, {"sql": "select * from `employees` where `employees`.`user_id` = 1 and `employees`.`user_id` is not null and `employees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.041961, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "layouts.administration.partials.topnav:185", "source": {"index": 21, "namespace": "view", "name": "layouts.administration.partials.topnav", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/administration/partials/topnav.blade.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fadministration%2Fpartials%2Ftopnav.blade.php&line=185", "ajax": false, "filename": "topnav.blade.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 99.052, "width_percent": 0.948}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 741, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 144, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 72, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}}, "count": 1014, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 81, "messages": [{"message": "[\n  ability => Role Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1198323687 data-indent-pad=\"  \"><span class=sf-dump-note>Role Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Role Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198323687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.992076, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1373425378 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373425378\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777709, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-705149743 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705149743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779177, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-787247462 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787247462\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780962, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2049126267 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049126267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.782687, "xdebug_link": null}, {"message": "[\n  ability => Attendance Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1157557364 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157557364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.784199, "xdebug_link": null}, {"message": "[\n  ability => Attendance Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1999469217 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Attendance Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999469217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.786082, "xdebug_link": null}, {"message": "[\n  ability => Attendance Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1166172424 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Attendance Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166172424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787413, "xdebug_link": null}, {"message": "[\n  ability => Attendance Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1521120287 data-indent-pad=\"  \"><span class=sf-dump-note>Attendance Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Attendance Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521120287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789034, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1327247357 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327247357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793683, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1408858301 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408858301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.795256, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-361061558 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Daily Break Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361061558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796918, "xdebug_link": null}, {"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-128850782 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128850782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.79847, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-950918020 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950918020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802265, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-81160075 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81160075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.804243, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-256180364 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256180364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.806545, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Delete,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1222387584 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222387584\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.808504, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1877367751 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Daily Work Update Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1877367751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.810483, "xdebug_link": null}, {"message": "[\n  ability => Daily Work Update Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1155351197 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Work Update Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Daily Work Update Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155351197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.812505, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1658397238 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658397238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.816733, "xdebug_link": null}, {"message": "[\n  ability => Task Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1481228066 data-indent-pad=\"  \"><span class=sf-dump-note>Task Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Task Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481228066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.818469, "xdebug_link": null}, {"message": "[\n  ability => Task Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2137455838 data-indent-pad=\"  \"><span class=sf-dump-note>Task Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Task Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137455838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.820523, "xdebug_link": null}, {"message": "[\n  ability => Task Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-260515972 data-indent-pad=\"  \"><span class=sf-dump-note>Task Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260515972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.825137, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-517118156 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517118156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829322, "xdebug_link": null}, {"message": "[\n  ability => Leave History Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1268935479 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268935479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.832296, "xdebug_link": null}, {"message": "[\n  ability => Leave History Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-603893402 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Leave History Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603893402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.835318, "xdebug_link": null}, {"message": "[\n  ability => Leave History Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-572913416 data-indent-pad=\"  \"><span class=sf-dump-note>Leave History Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Leave History Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572913416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.838074, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1291136361 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291136361\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.842539, "xdebug_link": null}, {"message": "[\n  ability => Announcement Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930027780 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Announcement Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930027780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.844171, "xdebug_link": null}, {"message": "[\n  ability => Announcement Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1522896722 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Announcement Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522896722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.845956, "xdebug_link": null}, {"message": "[\n  ability => Announcement Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079290169 data-indent-pad=\"  \"><span class=sf-dump-note>Announcement Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Announcement Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079290169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.847737, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1342693420 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342693420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852097, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1930188923 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IT Ticket Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930188923\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.854228, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1039548599 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039548599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856737, "xdebug_link": null}, {"message": "[\n  ability => IT Ticket Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1556668898 data-indent-pad=\"  \"><span class=sf-dump-note>IT Ticket Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">IT Ticket Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556668898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859178, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Everything,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1568551639 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Dining Room Booking Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568551639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.86345, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741786216 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741786216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865227, "xdebug_link": null}, {"message": "[\n  ability => Dining Room Booking Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-993269418 data-indent-pad=\"  \"><span class=sf-dump-note>Dining Room Booking Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Dining Room Booking Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993269418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867512, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-735441962 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735441962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871675, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-80352496 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80352496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877387, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612785616 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612785616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879777, "xdebug_link": null}, {"message": "[\n  ability => Penalty Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-106069718 data-indent-pad=\"  \"><span class=sf-dump-note>Penalty Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Penalty Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106069718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882885, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-906846616 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906846616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.909135, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-238598901 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238598901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.911524, "xdebug_link": null}, {"message": "[\n  ability => Quiz Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2111328101 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Quiz Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111328101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.913738, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1344689563 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344689563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916511, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1455666434 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455666434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.919291, "xdebug_link": null}, {"message": "[\n  ability => Quiz Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-422550933 data-indent-pad=\"  \"><span class=sf-dump-note>Quiz Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Quiz Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422550933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.92197, "xdebug_link": null}, {"message": "[\n  ability => Certificate Everything,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-299348022 data-indent-pad=\"  \"><span class=sf-dump-note>Certificate Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Certificate Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299348022\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.929176, "xdebug_link": null}, {"message": "[\n  ability => Certificate Create,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-149879881 data-indent-pad=\"  \"><span class=sf-dump-note>Certificate Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Certificate Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149879881\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.932077, "xdebug_link": null}, {"message": "[\n  ability => Certificate Read,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1664605375 data-indent-pad=\"  \"><span class=sf-dump-note>Certificate Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Certificate Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664605375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.933872, "xdebug_link": null}, {"message": "[\n  ability => Certificate Update,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-165567298 data-indent-pad=\"  \"><span class=sf-dump-note>Certificate Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Certificate Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165567298\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.935634, "xdebug_link": null}, {"message": "[\n  ability => Certificate Delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1292055530 data-indent-pad=\"  \"><span class=sf-dump-note>Certificate Delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Certificate Delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292055530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.937542, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1000020002 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000020002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.940512, "xdebug_link": null}, {"message": "[\n  ability => Vault Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14008859 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Vault Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14008859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942704, "xdebug_link": null}, {"message": "[\n  ability => Vault Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2026746258 data-indent-pad=\"  \"><span class=sf-dump-note>Vault Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Vault Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026746258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944312, "xdebug_link": null}, {"message": "[\n  ability => App Setting Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1818273418 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App Setting Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818273418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.951173, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-486411278 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486411278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.9526, "xdebug_link": null}, {"message": "[\n  ability => App Setting Update,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-152499452 data-indent-pad=\"  \"><span class=sf-dump-note>App Setting Update </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App Setting Update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152499452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953889, "xdebug_link": null}, {"message": "[\n  ability => Weekend Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1154821106 data-indent-pad=\"  \"><span class=sf-dump-note>Weekend Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Weekend Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154821106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956223, "xdebug_link": null}, {"message": "[\n  ability => Holiday Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1801192432 data-indent-pad=\"  \"><span class=sf-dump-note>Holiday Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Holiday Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801192432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.961451, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2102050711 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102050711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.963571, "xdebug_link": null}, {"message": "[\n  ability => User Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1598394390 data-indent-pad=\"  \"><span class=sf-dump-note>User Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">User Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598394390\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965661, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-775731729 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775731729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.967731, "xdebug_link": null}, {"message": "[\n  ability => User Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1027750041 data-indent-pad=\"  \"><span class=sf-dump-note>User Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">User Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027750041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.969787, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1484302519 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484302519\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971714, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1988590845 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988590845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.974755, "xdebug_link": null}, {"message": "[\n  ability => Role Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-240310464 data-indent-pad=\"  \"><span class=sf-dump-note>Role Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Role Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240310464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.976493, "xdebug_link": null}, {"message": "[\n  ability => Role Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1052387452 data-indent-pad=\"  \"><span class=sf-dump-note>Role Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Role Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052387452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.977987, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-354118481 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354118481\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.979678, "xdebug_link": null}, {"message": "[\n  ability => Permission Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102800182 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Permission Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102800182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.98138, "xdebug_link": null}, {"message": "[\n  ability => Permission Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-760025915 data-indent-pad=\"  \"><span class=sf-dump-note>Permission Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Permission Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760025915\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.982937, "xdebug_link": null}, {"message": "[\n  ability => Salary Everything,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2100523924 data-indent-pad=\"  \"><span class=sf-dump-note>Salary Everything </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Salary Everything</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100523924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.988022, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1906921144 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906921144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.993297, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1630904106 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630904106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996267, "xdebug_link": null}, {"message": "[\n  ability => Income Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-612642183 data-indent-pad=\"  \"><span class=sf-dump-note>Income Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Income Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612642183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.998233, "xdebug_link": null}, {"message": "[\n  ability => Income Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1555960477 data-indent-pad=\"  \"><span class=sf-dump-note>Income Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Income Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555960477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.999897, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1618685926 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618685926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.001838, "xdebug_link": null}, {"message": "[\n  ability => Expense Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1401643767 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Expense Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401643767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003765, "xdebug_link": null}, {"message": "[\n  ability => Expense Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2035272782 data-indent-pad=\"  \"><span class=sf-dump-note>Expense Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Expense Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035272782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.005731, "xdebug_link": null}, {"message": "[\n  ability => Logs Read,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1761913751 data-indent-pad=\"  \"><span class=sf-dump-note>Logs Read </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Logs Read</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761913751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011267, "xdebug_link": null}]}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/settings/rolepermission/permission/create\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752832853\n]", "alert": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/settings/rolepermission/role/all", "action_name": "administration.settings.rolepermission.role.index", "controller_action": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@index", "uri": "GET settings/rolepermission/role/all", "controller": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "settings/rolepermission/role", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FSettings%2FRole%2FRoleController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Settings/Role/RoleController.php:20-34</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Role Read", "duration": "3.49s", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-677013413 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-677013413\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-919108914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-919108914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Opera&quot;;v=&quot;120&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 OPR/120.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://blueorange.test/settings/rolepermission/permission/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,bn;q=0.8,hi;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1080 characters\">bo_multi_tenant_session=eyJpdiI6IkNoNmFHTk5KRGpkVkE2NXZ1dGF3YVE9PSIsInZhbHVlIjoicTE4eGg3eFB0anNKRVZNUUx4Nzg5TWtQU0tpL2ovZEg3a0JiQVN4ck1FS3NSOFBWcTlZNHZlc084Qk9ZTlB0aFlYWmdCRkRvV1BNbmk2ZGJmbEhyOXVtT1RtMml4a3k5bDlmcFpNQ1I0SmtsdG1sSEpBMjMrZmVaTFlJN1BmQ1UiLCJtYWMiOiJjYzdlM2Y5NmQ5MWY5ODM4YjU0ZWY2Y2IxOTZmZDY2ZGNjNzExMzliN2Q4MjcyYzY2MDVhM2JhOGQ4ZmRiMzk2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxKRWc4RklacnJZb3N5YU90VHhid0E9PSIsInZhbHVlIjoicmVXaDArcFBPUmdwS3FjVXIwS2dza0J5VWtxSE94QVBIbDFQbysyeWhmd0gxSWg1WFRGZEt0SW4wWGttejRMQUNRNDBQN2w5UUlFUVpuRGozY1VUOG1Sb1pSQmxDVlF6UWlKMG8wNUZUNkhqSWgwdzZjUEVIRXY4dyt0OVVDRWUiLCJtYWMiOiI0NjIwZDdmYTdhZWMwNmIxMzQxNjQ2NWU4MTRjNTI5OWJjNmVlNGQ0NmM3MzVkZWNlMmI2YjAwZWVhMzU4NzA5IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6ImpSM2pzQSs3V3BsYUs2RVg0YmMvUEE9PSIsInZhbHVlIjoiZFU0aVU4VC9qZElIVFE2V3pwSzBUN0dJSE0yd0E1NWNwZUlIZS9VYTJHSGwrd1ZHZWQwNjRnejl0KzlMYkRJdjI4bE8vZmNNaHFBVGZXL2hCR3puN3pCQVNraW9DQ0daRWZwb0FwaW9UUGg2RTk4KzlHeTMyaFZmd200WWxneU8iLCJtYWMiOiJlMjAxNmQzOGUzMTBmZTY0NzY2OWEzMWViMWZkYzM1NTUyY2E0ZDdkYzFjYzBmNTM4ZjkyODM0NjlmZGY3MDMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1278148413 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bo_multi_tenant_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU9hKUo8erm4hsoGsDajoymGto9RQabaIruS2lk5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278148413\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-374464910 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 11:30:23 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374464910\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-694876315 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://blueorange.test/settings/rolepermission/permission/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752832853</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694876315\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/settings/rolepermission/role/all", "action_name": "administration.settings.rolepermission.role.index", "controller_action": "App\\Http\\Controllers\\Administration\\Settings\\Role\\RoleController@index"}, "badge": null}}