<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Generate Certificate')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- Select2 CSS -->
    <link href="<?php echo e(asset('assets/css/custom_css/select2/select2.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/select2/select2-bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .placeholder-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .placeholder-item {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            margin-bottom: 2px;
        }
        .placeholder-item:hover {
            background-color: #f8f9fa;
        }
        .content-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Generate Certificate')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.certificate.index')); ?>"><?php echo e(__('All Certificates')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Generate Certificate')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Certificate Information')); ?></h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('administration.certificate.store')); ?>" method="POST" id="certificateForm">
                    <?php echo csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employee_id" class="form-label"><?php echo e(__('Select Employee')); ?> <span class="text-danger">*</span></label>
                            <select class="form-select select2" id="employee_id" name="employee_id" required>
                                <option value=""><?php echo e(__('Choose Employee')); ?></option>
                                <?php $__currentLoopData = $employees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($employee->id); ?>" <?php echo e(old('employee_id') == $employee->id ? 'selected' : ''); ?>>
                                        <?php echo e($employee->user->name); ?> (<?php echo e($employee->user->userid); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['employee_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="certificate_type" class="form-label"><?php echo e(__('Certificate Type')); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="certificate_type" name="certificate_type" required>
                                <option value=""><?php echo e(__('Choose Certificate Type')); ?></option>
                                <?php $__currentLoopData = $certificateTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($type); ?>" <?php echo e(old('certificate_type') == $type ? 'selected' : ''); ?>>
                                        <?php echo e($type); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['certificate_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="certificate_template_id" class="form-label"><?php echo e(__('Select Template (Optional)')); ?></label>
                            <select class="form-select select2" id="certificate_template_id" name="certificate_template_id">
                                <option value=""><?php echo e(__('Choose Template or Create Custom')); ?></option>
                                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($template->id); ?>" <?php echo e(old('certificate_template_id') == $template->id ? 'selected' : ''); ?>>
                                        <?php echo e($template->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['certificate_template_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="content" class="form-label"><?php echo e(__('Certificate Content')); ?> <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" placeholder="<?php echo e(__('Enter certificate content or select a template above')); ?>"><?php echo e(old('content')); ?></textarea>
                            <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate_pdf" name="generate_pdf" value="1" <?php echo e(old('generate_pdf') ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="generate_pdf">
                                    <?php echo e(__('Generate PDF immediately')); ?>

                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <button type="button" class="btn btn-outline-info me-2" id="previewBtn">
                                <i class="ti ti-eye me-1"></i><?php echo e(__('Preview')); ?>

                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Generate Certificate')); ?>

                            </button>
                            <a href="<?php echo e(route('administration.certificate.index')); ?>" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-1"></i><?php echo e(__('Back')); ?>

                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Available Placeholders')); ?></h6>
            </div>
            <div class="card-body">
                <div class="placeholder-list">
                    <?php $__currentLoopData = $placeholders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $placeholder => $description): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="placeholder-item" onclick="insertPlaceholder('<?php echo e($placeholder); ?>')">
                            <strong><?php echo e($placeholder); ?></strong><br>
                            <small class="text-muted"><?php echo e($description); ?></small>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Preview')); ?></h6>
            </div>
            <div class="card-body">
                <div id="contentPreview" class="content-preview">
                    <p class="text-muted"><?php echo e(__('Select an employee and click Preview to see the processed content')); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_links'); ?>
    
    <!-- Select2 JS -->
    <script src="<?php echo e(asset('assets/js/custom_js/select2/select2.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_js'); ?>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap4'
            });

            // Load template content when template is selected
            $('#certificate_template_id').change(function() {
                const templateId = $(this).val();
                if (templateId) {
                    $.get(`<?php echo e(route('administration.certificate.get-template', '')); ?>/${templateId}`)
                        .done(function(response) {
                            if (response.success) {
                                $('#content').val(response.content);
                            }
                        })
                        .fail(function() {
                            alert('<?php echo e(__("Error loading template content")); ?>');
                        });
                } else {
                    $('#content').val('');
                }
            });

            // Preview functionality
            $('#previewBtn').click(function() {
                const employeeId = $('#employee_id').val();
                const content = $('#content').val();
                const templateId = $('#certificate_template_id').val();

                if (!employeeId) {
                    alert('<?php echo e(__("Please select an employee first")); ?>');
                    return;
                }

                if (!content && !templateId) {
                    alert('<?php echo e(__("Please enter content or select a template")); ?>');
                    return;
                }

                $.post('<?php echo e(route('administration.certificate.preview')); ?>', {
                    _token: '<?php echo e(csrf_token()); ?>',
                    employee_id: employeeId,
                    content: content,
                    certificate_template_id: templateId
                })
                .done(function(response) {
                    if (response.success) {
                        $('#contentPreview').html(response.content);
                    }
                })
                .fail(function(xhr) {
                    alert('<?php echo e(__("Error generating preview")); ?>');
                });
            });
        });

        function insertPlaceholder(placeholder) {
            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;
            
            textarea.value = text.substring(0, start) + placeholder + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/certificate/create.blade.php ENDPATH**/ ?>