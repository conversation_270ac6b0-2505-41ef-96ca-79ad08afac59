<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Administration\Certificate\CertificateTemplateController;
use App\Http\Controllers\Administration\Certificate\EmployeeCertificateController;

/* ==============================================
===============< Certificate Routes >============
===============================================*/
Route::prefix('certificate')
    ->name('certificate.')
    ->group(function () {
        
        // Certificate Template Routes
        Route::prefix('template')
            ->name('template.')
            ->controller(CertificateTemplateController::class)
            ->group(function () {
                Route::get('/all', 'index')->name('index')->can('Certificate Read');
                Route::get('/create', 'create')->name('create')->can('Certificate Create');
                Route::post('/store', 'store')->name('store')->can('Certificate Create');
                Route::get('/show/{template}', 'show')->name('show')->can('Certificate Read');
                Route::get('/edit/{template}', 'edit')->name('edit')->can('Certificate Update');
                Route::put('/update/{template}', 'update')->name('update')->can('Certificate Update');
                Route::delete('/destroy/{template}', 'destroy')->name('destroy')->can('Certificate Delete');
                Route::patch('/toggle-status/{template}', 'toggleStatus')->name('toggle-status')->can('Certificate Update');
            });

        // Employee Certificate Routes
        Route::controller(EmployeeCertificateController::class)
            ->group(function () {
                Route::get('/all', 'index')->name('index')->can('Certificate Read');
                Route::get('/create', 'create')->name('create')->can('Certificate Create');
                Route::post('/store', 'store')->name('store')->can('Certificate Create');
                Route::get('/show/{certificate}', 'show')->name('show')->can('Certificate Read');
                Route::delete('/destroy/{certificate}', 'destroy')->name('destroy')->can('Certificate Delete');
                
                // PDF Generation and Preview
                Route::post('/generate-pdf/{certificate}', 'generatePDF')->name('generate-pdf')->can('Certificate Create');
                Route::get('/pdf/{certificate}', 'pdf')->name('pdf')->can('Certificate Read');
                
                // AJAX endpoints
                Route::post('/preview', 'preview')->name('preview')->can('Certificate Create');
                Route::get('/get-template/{template}', 'getTemplate')->name('get-template')->can('Certificate Create');
            });
    });
