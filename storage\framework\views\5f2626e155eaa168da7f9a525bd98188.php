<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Income & Expense Categories')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Categories')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Accounts')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Income & Expenses')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Categories')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">All Categories</h5>
        
                <div class="card-header-elements ms-auto">
                    <a href="javascript:void(0);" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#assignNewCategoryModal">
                        <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                        Assign Category
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Total Income</th>
                                <th>Total Expense</th>
                                <th>Profit/Loss</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                <tr>
                                    <th><?php echo e(serial($categories, $key)); ?></th>
                                    <td><?php echo e($category->name); ?></td>
                                    <td>
                                        <?php
                                            $status = $category->is_active == true ? 'Active' : 'Inactive';
                                            $background = $category->is_active == true ? 'bg-success' : 'bg-danger';
                                        ?>
                                        <span class="badge <?php echo e($background); ?>"><?php echo e($status); ?></span>
                                    </td>
                                    <td>
                                        <b class="text-success"><?php echo e(format_currency($category->total_income)); ?></b>
                                        <br>
                                        <span class="text-muted">
                                            <span class="text-dark text-bold">Incomes: </span>
                                            <?php echo e($category->incomes_count); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <b class="text-danger"><?php echo e(format_currency($category->total_expense)); ?></b>
                                        <br>
                                        <span class="text-muted">
                                            <span class="text-dark text-bold">Expenses: </span>
                                            <?php echo e($category->expenses_count); ?>

                                        </span>
                                    </td>                                    
                                    <td>
                                        <?php
                                            $profitOrLoss = ($category->total_income - $category->total_expense) > 0 ? 'Profit' : 'Loss';
                                            $color = ($category->total_income - $category->total_expense) > 0 ? 'text-success' : 'text-danger';
                                        ?>
                                        <b class="<?php echo e($color); ?>" title="Total <?php echo e($profitOrLoss); ?>">
                                            <?php echo e(format_currency($category->total_income - $category->total_expense)); ?>

                                        </b>
                                    </td>
                                    <td class="text-center">
                                        <a href="<?php echo e(route('administration.accounts.income_expense.category.show', ['category' => $category])); ?>" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" title="Show Details">
                                            <i class="text-white ti ti-info-hexagon"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>        
    </div>
</div>
<!-- End row -->



<?php echo $__env->make('administration.accounts.income_expense.category.modals.category_create', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.date-picker').datepicker({
                format: 'yyyy-mm-dd',
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });

            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });

            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/accounts/income_expense/category/index.blade.php ENDPATH**/ ?>