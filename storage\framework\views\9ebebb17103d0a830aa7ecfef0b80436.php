<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Certificate Templates')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Certificate Templates')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Certificate Templates')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Basic Bootstrap Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><?php echo e(__('Certificate Templates')); ?></h5>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Create'])): ?>
            <a href="<?php echo e(route('administration.certificate.template.create')); ?>" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Template')); ?>

            </a>
        <?php endif; ?>
    </div>

    <div class="card-body">
        <div class="table-responsive-md table-responsive-sm w-100">
            <table class="table data-table table-bordered">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Title')); ?></th>
                        <th><?php echo e(__('Description')); ?></th>
                        <th><?php echo e(__('Content Preview')); ?></th>
                        <th><?php echo e(__('Status')); ?></th>
                        <th><?php echo e(__('Created By')); ?></th>
                        <th><?php echo e(__('Created At')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($key + 1); ?></td>
                            <td>
                                <strong><?php echo e($template->title); ?></strong>
                            </td>
                            <td>
                                <small><?php echo e($template->description ?? __('No description')); ?></small>
                            </td>
                            <td>
                                <small class="text-muted"><?php echo e($template->content_preview); ?></small>
                            </td>
                            <td>
                                <span class="<?php echo e($template->status_badge_class); ?>"><?php echo e($template->status_text); ?></span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs me-2">
                                        <?php if($template->creator->media->isNotEmpty()): ?>
                                            <img src="<?php echo e($template->creator->media->first()->getUrl()); ?>" alt="Avatar" class="rounded-circle">
                                        <?php else: ?>
                                            <span class="avatar-initial rounded-circle bg-label-secondary"><?php echo e(substr($template->creator->name, 0, 1)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <small><?php echo e($template->creator->name); ?></small>
                                </div>
                            </td>
                            <td>
                                <small><?php echo e($template->created_at->format('M d, Y h:i A')); ?></small>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                        <i class="ti ti-dots-vertical"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Read'])): ?>
                                            <a class="dropdown-item" href="<?php echo e(route('administration.certificate.template.show', $template)); ?>">
                                                <i class="ti ti-eye me-1"></i><?php echo e(__('View')); ?>

                                            </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Update'])): ?>
                                            <a class="dropdown-item" href="<?php echo e(route('administration.certificate.template.edit', $template)); ?>">
                                                <i class="ti ti-edit me-1"></i><?php echo e(__('Edit')); ?>

                                            </a>
                                            <a class="dropdown-item" href="javascript:void(0);" onclick="toggleStatus(<?php echo e($template->id); ?>)">
                                                <i class="ti ti-toggle-<?php echo e($template->is_active ? 'left' : 'right'); ?> me-1"></i>
                                                <?php echo e($template->is_active ? __('Deactivate') : __('Activate')); ?>

                                            </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Certificate Everything', 'Certificate Delete'])): ?>
                                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="confirmDelete(<?php echo e($template->id); ?>)">
                                                <i class="ti ti-trash me-1"></i><?php echo e(__('Delete')); ?>

                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo e(__('No templates found')); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_links'); ?>
    
    <!-- DataTables js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_js'); ?>
    <script>
        $(document).ready(function() {
            $('.data-table').DataTable({
                "order": [[ 6, "desc" ]]
            });
        });

        function toggleStatus(templateId) {
            if (confirm('<?php echo e(__("Are you sure you want to change the status of this template?")); ?>')) {
                $.ajax({
                    url: `<?php echo e(route('administration.certificate.template.toggle-status', '')); ?>/${templateId}`,
                    type: 'PATCH',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('<?php echo e(__("Error changing template status. Please try again.")); ?>');
                    }
                });
            }
        }

        function confirmDelete(templateId) {
            if (confirm('<?php echo e(__("Are you sure you want to delete this template? This action cannot be undone.")); ?>')) {
                $.ajax({
                    url: `<?php echo e(route('administration.certificate.template.destroy', '')); ?>/${templateId}`,
                    type: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('<?php echo e(__("Error deleting template. Please try again.")); ?>');
                    }
                });
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/certificate/template/index.blade.php ENDPATH**/ ?>