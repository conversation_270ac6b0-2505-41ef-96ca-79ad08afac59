@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Create Certificate Template'))

@section('css_links')
    {{--  External CSS  --}}
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        .placeholder-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .placeholder-item {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 4px;
            border: 1px solid #e3e6f0;
        }
        .placeholder-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Create Certificate Template') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('administration.dashboard.index') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item">
        <a href="{{ route('administration.certificate.template.index') }}">{{ __('Certificate Templates') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('Create Template') }}</li>
@endsection

@section('content')

<!-- Instructions Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="ti ti-info-circle me-2"></i>{{ __('How to Create Certificate Templates') }}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">{{ __('Step 1: Template Information') }}</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Enter a descriptive template title') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Add an optional description for reference') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Keep the template active for immediate use') }}</li>
                        </ul>

                        <h6 class="text-primary mt-3">{{ __('Step 2: Template Content') }}</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Write your certificate content in the text area') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Use placeholders for dynamic employee data') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Click placeholders from the right panel to insert them') }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">{{ __('Step 3: Using Placeholders') }}</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-lightbulb text-warning me-2"></i>{{ __('Placeholders are replaced with actual employee data') }}</li>
                            <li><i class="ti ti-lightbulb text-warning me-2"></i>{{ __('Example: {{employee_name}} becomes "John Doe"') }}</li>
                            <li><i class="ti ti-lightbulb text-warning me-2"></i>{{ __('Use {{current_date}} for today\'s date') }}</li>
                        </ul>

                        <h6 class="text-primary mt-3">{{ __('Step 4: Save & Use') }}</h6>
                        <ul class="list-unstyled">
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Click "Create Template" to save') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('Template will be available for certificate generation') }}</li>
                            <li><i class="ti ti-check text-success me-2"></i>{{ __('You can edit or deactivate templates later') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Template Information') }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('administration.certificate.template.store') }}" method="POST" id="templateForm">
                    @csrf

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">{{ __('Template Title') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}" required placeholder="{{ __('Enter template title') }}">
                            @error('title')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">{{ __('Description (Optional)') }}</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="{{ __('Enter template description') }}">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="content" class="form-label">{{ __('Template Content') }} <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="15" required placeholder="{{ __('Enter certificate template content with placeholders') }}">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">{{ __('Use placeholders from the right panel to dynamically insert employee data') }}</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    {{ __('Active Template') }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-1"></i>{{ __('Create Template') }}
                            </button>
                            <a href="{{ route('administration.certificate.template.index') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-1"></i>{{ __('Back') }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Available Placeholders') }}</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">{{ __('Click on any placeholder to insert it into the template content') }}</p>
                <div class="placeholder-list">
                    @foreach($placeholders as $placeholder => $description)
                        <div class="placeholder-item" onclick="insertPlaceholder('{{ $placeholder }}')">
                            <strong>{{ $placeholder }}</strong><br>
                            <small class="text-muted">{{ $description }}</small>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Sample Template') }}</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>{{ __('Example Template:') }}</strong><br><br>

                    {{ __('This is to certify that') }} <strong>{{'{{'}}employee_name{{'}}'}}</strong> {{ __('has been employed with') }} <strong>{{'{{'}}company_name{{'}}'}}</strong> {{ __('since') }} <strong>{{'{{'}}joining_date{{'}}'}}</strong>.<br><br>

                    {{ __('During the employment period, the employee has shown excellent performance and dedication.') }}<br><br>

                    {{ __('This certificate is issued on') }} <strong>{{'{{'}}current_date{{'}}'}}</strong>.
                </small>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Certificate Types') }}</h6>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-1">
                    @foreach($certificateTypes as $type)
                        <span class="badge bg-light text-dark">{{ $type }}</span>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js_links')
    {{--  External JS  --}}
@endsection

@section('custom_js')
    <script>
        function insertPlaceholder(placeholder) {
            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            textarea.value = text.substring(0, start) + placeholder + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
        }

        // Form validation
        document.getElementById('templateForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = document.getElementById('content').value.trim();

            if (!title) {
                e.preventDefault();
                alert('{{ __("Please enter a template title") }}');
                document.getElementById('title').focus();
                return;
            }

            if (!content) {
                e.preventDefault();
                alert('{{ __("Please enter template content") }}');
                document.getElementById('content').focus();
                return;
            }
        });
    </script>
@endsection
