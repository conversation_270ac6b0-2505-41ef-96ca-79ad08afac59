{"__meta": {"id": "01K10D90S3YXX09SW0T2RQX5CN", "datetime": "2025-07-25 15:05:25", "utime": **********.798062, "method": "GET", "uri": "/dashboard/calendar/events?start=2025-06-28T00%3A00%3A00%2B06%3A00&end=2025-08-09T00%3A00%3A00%2B06%3A00", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753434318.885188, "end": **********.798107, "duration": 6.91291880607605, "duration_str": "6.91s", "measures": [{"label": "Booting", "start": 1753434318.885188, "relative_start": 0, "end": **********.212051, "relative_end": **********.212051, "duration": 1.****************, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.212077, "relative_start": 1.****************, "end": **********.79811, "relative_end": 3.0994415283203125e-06, "duration": 5.**************, "duration_str": "5.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.271763, "relative_start": 1.****************, "end": **********.29349, "relative_end": **********.29349, "duration": 0.*****************, "duration_str": "21.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.789074, "relative_start": 6.***************, "end": **********.790548, "relative_end": **********.790548, "duration": 0.001474142074584961, "duration_str": "1.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboard/calendar/events", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "controller": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.dashboard.calendar.events", "prefix": "/dashboard/calendar", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php:42-83</a>"}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05000000000000001, "accumulated_duration_str": "50ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.466418, "duration": 0.011810000000000001, "duration_str": "11.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 23.62}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.497846, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 23.62, "width_percent": 2.22}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.5093088, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 25.84, "width_percent": 1.54}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.5126278, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 27.38, "width_percent": 1.56}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5308912, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 28.94, "width_percent": 3.72}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.539145, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 32.66, "width_percent": 1.64}, {"sql": "select * from `tasks` where `creator_id` = 1 and `deadline` is not null and `deadline` between '2025-06-28T00:00:00+06:00' and '2025-08-09T00:00:00+06:00' and `tasks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "2025-06-28T00:00:00+06:00", "2025-08-09T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.642323, "duration": 0.01516, "duration_str": "15.16ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:101", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=101", "ajax": false, "filename": "DashboardCalendarController.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 34.3, "width_percent": 30.32}, {"sql": "select * from `tasks` where exists (select * from `users` inner join `task_user` on `users`.`id` = `task_user`.`user_id` where `tasks`.`id` = `task_user`.`task_id` and `user_id` = 1 and `users`.`deleted_at` is null) and `deadline` is not null and `deadline` between '2025-06-28T00:00:00+06:00' and '2025-08-09T00:00:00+06:00' and `tasks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "2025-06-28T00:00:00+06:00", "2025-08-09T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.669988, "duration": 0.00601, "duration_str": "6.01ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:126", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=126", "ajax": false, "filename": "DashboardCalendarController.php", "line": "126"}, "connection": "blueorange", "explain": null, "start_percent": 64.62, "width_percent": 12.02}, {"sql": "select * from `holidays` where `is_active` = 1 and `date` between '2025-06-28T00:00:00+06:00' and '2025-08-09T00:00:00+06:00' and `holidays`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "2025-06-28T00:00:00+06:00", "2025-08-09T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 161}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.736049, "duration": 0.00753, "duration_str": "7.53ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:161", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=161", "ajax": false, "filename": "DashboardCalendarController.php", "line": "161"}, "connection": "blueorange", "explain": null, "start_percent": 76.64, "width_percent": 15.06}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 260}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 193}], "start": **********.7583911, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 91.7, "width_percent": 3.08}, {"sql": "select * from `leave_histories` where `status` = 'Approved' and `date` between '2025-06-28T00:00:00+06:00' and '2025-08-09T00:00:00+06:00' and `leave_histories`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Approved", "2025-06-28T00:00:00+06:00", "2025-08-09T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 209}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.764401, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:209", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=209", "ajax": false, "filename": "DashboardCalendarController.php", "line": "209"}, "connection": "blueorange", "explain": null, "start_percent": 94.78, "width_percent": 2.98}, {"sql": "select `day` from `weekends` where `is_active` = 1 and `weekends`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Weekend/Weekend.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Weekend\\Weekend.php", "line": 26}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 254}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.769795, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Weekend.php:26", "source": {"index": 14, "namespace": null, "name": "app/Models/Weekend/Weekend.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Weekend\\Weekend.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FWeekend%2FWeekend.php&line=26", "ajax": false, "filename": "Weekend.php", "line": "26"}, "connection": "blueorange", "explain": null, "start_percent": 97.76, "width_percent": 2.24}]}, "models": {"data": {"App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Task\\Task": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FTask%2FTask.php&line=1", "ajax": false, "filename": "Task.php", "line": "?"}}}, "count": 7, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/dashboard/calendar/events?end=2025-08-09T00%3A00%3A00%2B06%3A00&start=2025-06-28T00%3A00%3A00%2B06%3A00\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752832853\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/dashboard/calendar/events?end=2025-08-09T00%3A00%3A00%2B06%3A00&start=2025-0...", "action_name": "administration.dashboard.calendar.events", "controller_action": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents", "uri": "GET dashboard/calendar/events", "controller": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/dashboard/calendar", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php:42-83</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "duration": "6.92s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-349074803 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str title=\"25 characters\">2025-06-28T00:00:00+06:00</span>\"\n  \"<span class=sf-dump-key>end</span>\" => \"<span class=sf-dump-str title=\"25 characters\">2025-08-09T00:00:00+06:00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349074803\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1314991259 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1314991259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1868697419 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 OPR/120.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Opera&quot;;v=&quot;120&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1080 characters\">bo_multi_tenant_session=eyJpdiI6IkNoNmFHTk5KRGpkVkE2NXZ1dGF3YVE9PSIsInZhbHVlIjoicTE4eGg3eFB0anNKRVZNUUx4Nzg5TWtQU0tpL2ovZEg3a0JiQVN4ck1FS3NSOFBWcTlZNHZlc084Qk9ZTlB0aFlYWmdCRkRvV1BNbmk2ZGJmbEhyOXVtT1RtMml4a3k5bDlmcFpNQ1I0SmtsdG1sSEpBMjMrZmVaTFlJN1BmQ1UiLCJtYWMiOiJjYzdlM2Y5NmQ5MWY5ODM4YjU0ZWY2Y2IxOTZmZDY2ZGNjNzExMzliN2Q4MjcyYzY2MDVhM2JhOGQ4ZmRiMzk2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1NNjl6NFhodndmUWYzcVdqaUI4clE9PSIsInZhbHVlIjoidVJFbXpuUTh1UUVJdlpkeE5BeGFxdW9xMTlNSTdzTHZGZ0VDOFNBSk8yeXVkd1JreURUTS91T0hVaW92ZEsvc3JYekVydUlqUTdRRWNSdUx2QjlOUkxqUGkxUm5MTFJIbmdyb1drQlFIRjFZc1ZFOEFPdXpEeFc2cnByQ2lReWQiLCJtYWMiOiI3OTUwZDA1NWYzMDc4YWU2MmJjYWQ1OGJkYmM4YTcwNDgxMTI2NDgwZGZhMmQ5NjY1OTUxYmU5NWU3OTc4ZGM0IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6Im50OUZsbEJ2SlhoRjVqZW4rbEZtOXc9PSIsInZhbHVlIjoiRGpzc1R1cTNpTG5lQk9SdmNxTWxqVS9OLzlpWHVKK2s1SHVvYmtlbjVvL0dxUGR0NldyL1U3dnRZTGVtMXZaYWNmMFJmUGRqOGdMTGxOT1dlNEcvWnJWMWZ2TUlwSGZtZzJlSUNCd2ZFNUpmRUxUOTFNbkhaRmlWRFc3NkwrWTAiLCJtYWMiOiJjOTFkYzg0NjM3OTQ1MWU3YjhjMGM1ZGI5NDMyZmYwNDYwOTRkMTZmNTg5YTI1YTgyZjRmNDk0YzczMjcxYzQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868697419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>bo_multi_tenant_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU9hKUo8erm4hsoGsDajoymGto9RQabaIruS2lk5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1130874759 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 09:05:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130874759\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2101458313 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"127 characters\">https://blueorange.test/dashboard/calendar/events?end=2025-08-09T00%3A00%3A00%2B06%3A00&amp;start=2025-06-28T00%3A00%3A00%2B06%3A00</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752832853</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101458313\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/dashboard/calendar/events?end=2025-08-09T00%3A00%3A00%2B06%3A00&start=2025-0...", "action_name": "administration.dashboard.calendar.events", "controller_action": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents"}, "badge": null}}