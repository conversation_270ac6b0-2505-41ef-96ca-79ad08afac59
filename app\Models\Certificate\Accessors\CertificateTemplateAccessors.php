<?php

namespace App\Models\Certificate\Accessors;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait CertificateTemplateAccessors
{
    /**
     * Get the status text based on is_active field
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->is_active ? 'Active' : 'Inactive';
            }
        );
    }

    /**
     * Get the status badge class for UI
     */
    protected function statusBadgeClass(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->is_active ? 'badge bg-success' : 'badge bg-secondary';
            }
        );
    }

    /**
     * Get a preview of the content (first 100 characters)
     */
    protected function contentPreview(): Attribute
    {
        return Attribute::make(
            get: function () {
                return strlen($this->content) > 100 
                    ? substr($this->content, 0, 100) . '...' 
                    : $this->content;
            }
        );
    }
}
