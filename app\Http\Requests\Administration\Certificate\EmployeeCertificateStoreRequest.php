<?php

namespace App\Http\Requests\Administration\Certificate;

use Illuminate\Foundation\Http\FormRequest;

class EmployeeCertificateStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->can('Certificate Create');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => ['required', 'exists:employees,id'],
            'certificate_template_id' => ['nullable', 'exists:certificate_templates,id'],
            'certificate_type' => ['required', 'string', 'max:255'],
            'content' => ['required_without:certificate_template_id', 'string'],
            'generate_pdf' => ['boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'employee_id.required' => 'Please select an employee.',
            'employee_id.exists' => 'The selected employee is invalid.',
            'certificate_template_id.exists' => 'The selected certificate template is invalid.',
            'certificate_type.required' => 'The certificate type is required.',
            'content.required_without' => 'The certificate content is required when no template is selected.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee',
            'certificate_template_id' => 'certificate template',
            'certificate_type' => 'certificate type',
            'content' => 'certificate content',
            'generate_pdf' => 'generate PDF',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'generate_pdf' => $this->boolean('generate_pdf'),
        ]);
    }
}
