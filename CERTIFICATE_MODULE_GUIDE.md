# 📜 Employee Certificate Generation Module - User Guide

## 🎯 Overview
The Employee Certificate Generation module allows HR managers and authorized personnel to create, manage, and generate professional certificates for employees. This includes employment verification letters, experience certificates, salary certificates, and more.

## 🚀 Getting Started

### Prerequisites
- User must have appropriate Certificate permissions (Create, Read, Update, Delete)
- Employee data must be available in the system
- Spatie Browsershot package for PDF generation

### Initial Setup
1. **Run Migrations**: `php artisan migrate`
2. **Seed Permissions**: `php artisan db:seed --class=Database\\Seeders\\Permission\\PermissionsTableSeeder`
3. **Seed Sample Templates**: `php artisan db:seed --class=Database\\Seeders\\Certificate\\CertificateTemplateSeeder`
4. **Assign Permissions**: Give users appropriate Certificate permissions through the admin panel

## 📋 Module Features

### 1. Certificate Template Management
Create reusable templates with dynamic placeholders that automatically populate with employee data.

**Available Placeholders:**
- `{{employee_name}}` - Full employee name
- `{{employee_first_name}}` - First name only
- `{{employee_last_name}}` - Last name only
- `{{employee_email}}` - Employee email address
- `{{employee_userid}}` - Employee ID
- `{{employee_alias_name}}` - Employee alias/nickname
- `{{joining_date}}` - Employee joining date
- `{{current_date}}` - Today's date
- `{{company_name}}` - Company name from config
- `{{employee_contact}}` - Employee contact number
- `{{employee_father_name}}` - Father's name
- `{{employee_mother_name}}` - Mother's name
- `{{employee_birth_date}}` - Date of birth
- `{{employee_gender}}` - Gender
- `{{employee_blood_group}}` - Blood group

### 2. Certificate Types Supported
- Appointment Letter
- Employment Verification Letter
- Experience Certificate
- Salary Certificate
- Appreciation Certificate
- Award Certificate
- Internship Completion Certificate
- NOC (No Objection Certificate) Letter
- Visa Letter
- Custom Certificate

### 3. PDF Generation
Professional PDF certificates with:
- Company logo and branding
- Authorized signature
- Certificate ID for tracking
- Watermark for authenticity
- Responsive design for printing

## 📖 Step-by-Step User Guide

### Creating Certificate Templates

1. **Navigate to Certificate Templates**
   - Go to Certificate > Templates > Create Template

2. **Fill Template Information**
   - **Title**: Enter a descriptive name (e.g., "Employment Verification Letter")
   - **Description**: Optional description for reference
   - **Active Status**: Keep checked for immediate use

3. **Create Template Content**
   - Write your certificate content in the text area
   - Use placeholders for dynamic data
   - Click placeholders from the right panel to insert them
   - Preview your template using the sample section

4. **Save Template**
   - Click "Create Template" to save
   - Template will be available for certificate generation

### Generating Employee Certificates

1. **Navigate to Certificate Generation**
   - Go to Certificate > Generate Certificate

2. **Select Employee**
   - Choose the employee from the dropdown
   - Search by name or employee ID

3. **Choose Certificate Type**
   - Select from predefined types or choose "Custom Certificate"

4. **Select Template (Optional)**
   - Choose a pre-made template OR
   - Leave blank to create custom content

5. **Edit Content**
   - Modify template content if needed
   - Add custom content for unique certificates
   - Use placeholders for dynamic data

6. **Preview Certificate**
   - Click "Preview" to see processed content
   - Verify all placeholders are correctly replaced

7. **Generate Certificate**
   - Check "Generate PDF immediately" for instant PDF creation
   - Click "Generate Certificate" to save

### Managing Certificates

1. **View All Certificates**
   - Go to Certificate > All Certificates
   - Filter by employee, type, or date range

2. **Certificate Actions**
   - **View**: See certificate details and content
   - **Generate PDF**: Create PDF if not already generated
   - **Download**: Download existing PDF
   - **Delete**: Remove certificate (with confirmation)

### Template Management

1. **View Templates**
   - Go to Certificate > Templates > All Templates
   - See active/inactive status and usage

2. **Template Actions**
   - **Edit**: Modify template content
   - **Activate/Deactivate**: Control availability
   - **Delete**: Remove template permanently

## 🔧 Advanced Features

### Custom Content Creation
- Create certificates without templates
- Full control over content and formatting
- Use any combination of placeholders

### Batch Operations
- Generate multiple certificates using the same template
- Consistent formatting across all certificates

### Audit Trail
- Track who generated each certificate
- See creation dates and template usage
- Monitor certificate generation activity

## 🛡️ Security Features

### Permission-Based Access
- **Certificate Everything**: Full access to all features
- **Certificate Create**: Generate new certificates and templates
- **Certificate Read**: View certificates and templates
- **Certificate Update**: Edit templates and settings
- **Certificate Delete**: Remove certificates and templates

### Data Protection
- Secure file storage
- Encrypted certificate IDs in URLs
- User interaction restrictions
- Soft delete for data recovery

## 📊 Best Practices

### Template Design
1. **Use Clear Language**: Write professional, clear content
2. **Include All Necessary Information**: Company details, employee info, purpose
3. **Consistent Formatting**: Use standard business letter format
4. **Placeholder Usage**: Use appropriate placeholders for dynamic content

### Certificate Generation
1. **Verify Employee Data**: Ensure employee information is up-to-date
2. **Preview Before Generating**: Always preview to check placeholder replacement
3. **Choose Appropriate Type**: Select the correct certificate type for the purpose
4. **Generate PDF**: Create PDF for official use and record-keeping

### Template Management
1. **Regular Updates**: Keep templates current with company policies
2. **Version Control**: Create new templates instead of modifying heavily-used ones
3. **Descriptive Names**: Use clear, descriptive template titles
4. **Active Management**: Deactivate outdated templates instead of deleting

## 🔍 Troubleshooting

### Common Issues

**Template Not Loading**
- Check if template is active
- Verify user has appropriate permissions
- Refresh the page and try again

**Placeholders Not Replacing**
- Ensure employee data exists in the system
- Check placeholder spelling (case-sensitive)
- Verify employee is selected before preview

**PDF Generation Failed**
- Check Browsershot configuration
- Verify Node.js is installed and accessible
- Check file permissions for storage directory

**Permission Denied**
- Verify user has Certificate permissions
- Check user interaction settings
- Contact administrator for permission assignment

## 📞 Support

For technical issues or feature requests:
1. Check this guide first
2. Verify permissions and data
3. Contact system administrator
4. Report bugs with detailed steps to reproduce

## 🔄 Updates and Maintenance

### Regular Tasks
- Review and update templates quarterly
- Clean up old certificate files periodically
- Monitor certificate generation usage
- Update employee data for accuracy

### System Maintenance
- Backup certificate templates regularly
- Monitor PDF generation performance
- Update Browsershot package as needed
- Review and update permissions as staff changes

---

**Version**: 1.0  
**Last Updated**: July 25, 2025  
**Module**: Employee Certificate Generation  
**Application**: BlueOrange Office Management System
