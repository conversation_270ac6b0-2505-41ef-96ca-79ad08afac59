<?php

namespace App\Models\Certificate\Mutators;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait CertificateTemplateMutators
{
    /**
     * Set the title with proper formatting
     */
    protected function title(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return trim($value);
            }
        );
    }

    /**
     * Set the content with proper formatting
     */
    protected function content(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return trim($value);
            }
        );
    }

    /**
     * Set the description with proper formatting
     */
    protected function description(): Attribute
    {
        return Attribute::make(
            set: function ($value) {
                return $value ? trim($value) : null;
            }
        );
    }
}
