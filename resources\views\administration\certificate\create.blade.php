@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Generate Certificate'))

@section('css_links')
    {{--  External CSS  --}}
    <!-- Select2 CSS -->
    <link href="{{ asset('assets/css/custom_css/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/css/custom_css/select2/select2-bootstrap4.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        .placeholder-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .placeholder-item {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            margin-bottom: 2px;
        }
        .placeholder-item:hover {
            background-color: #f8f9fa;
        }
        .content-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
        }
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Generate Certificate') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('administration.dashboard.index') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item">
        <a href="{{ route('administration.certificate.index') }}">{{ __('All Certificates') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('Generate Certificate') }}</li>
@endsection

@section('content')

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Certificate Information') }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('administration.certificate.store') }}" method="POST" id="certificateForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employee_id" class="form-label">{{ __('Select Employee') }} <span class="text-danger">*</span></label>
                            <select class="form-select select2" id="employee_id" name="employee_id" required>
                                <option value="">{{ __('Choose Employee') }}</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->user->name }} ({{ $employee->user->userid }})
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="certificate_type" class="form-label">{{ __('Certificate Type') }} <span class="text-danger">*</span></label>
                            <select class="form-select" id="certificate_type" name="certificate_type" required>
                                <option value="">{{ __('Choose Certificate Type') }}</option>
                                @foreach($certificateTypes as $type)
                                    <option value="{{ $type }}" {{ old('certificate_type') == $type ? 'selected' : '' }}>
                                        {{ $type }}
                                    </option>
                                @endforeach
                            </select>
                            @error('certificate_type')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="certificate_template_id" class="form-label">{{ __('Select Template (Optional)') }}</label>
                            <select class="form-select select2" id="certificate_template_id" name="certificate_template_id">
                                <option value="">{{ __('Choose Template or Create Custom') }}</option>
                                @foreach($templates as $template)
                                    <option value="{{ $template->id }}" {{ old('certificate_template_id') == $template->id ? 'selected' : '' }}>
                                        {{ $template->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('certificate_template_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="content" class="form-label">{{ __('Certificate Content') }} <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" placeholder="{{ __('Enter certificate content or select a template above') }}">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate_pdf" name="generate_pdf" value="1" {{ old('generate_pdf') ? 'checked' : '' }}>
                                <label class="form-check-label" for="generate_pdf">
                                    {{ __('Generate PDF immediately') }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <button type="button" class="btn btn-outline-info me-2" id="previewBtn">
                                <i class="ti ti-eye me-1"></i>{{ __('Preview') }}
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-device-floppy me-1"></i>{{ __('Generate Certificate') }}
                            </button>
                            <a href="{{ route('administration.certificate.index') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-1"></i>{{ __('Back') }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Available Placeholders') }}</h6>
            </div>
            <div class="card-body">
                <div class="placeholder-list">
                    @foreach($placeholders as $placeholder => $description)
                        <div class="placeholder-item" onclick="insertPlaceholder('{{ $placeholder }}')">
                            <strong>{{ $placeholder }}</strong><br>
                            <small class="text-muted">{{ $description }}</small>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">{{ __('Preview') }}</h6>
            </div>
            <div class="card-body">
                <div id="contentPreview" class="content-preview">
                    <p class="text-muted">{{ __('Select an employee and click Preview to see the processed content') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js_links')
    {{--  External JS  --}}
    <!-- Select2 JS -->
    <script src="{{ asset('assets/js/custom_js/select2/select2.min.js') }}"></script>
@endsection

@section('custom_js')
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap4'
            });

            // Load template content when template is selected
            $('#certificate_template_id').change(function() {
                const templateId = $(this).val();
                if (templateId) {
                    $.get(`{{ route('administration.certificate.get-template', '') }}/${templateId}`)
                        .done(function(response) {
                            if (response.success) {
                                $('#content').val(response.content);
                            }
                        })
                        .fail(function() {
                            alert('{{ __("Error loading template content") }}');
                        });
                } else {
                    $('#content').val('');
                }
            });

            // Preview functionality
            $('#previewBtn').click(function() {
                const employeeId = $('#employee_id').val();
                const content = $('#content').val();
                const templateId = $('#certificate_template_id').val();

                if (!employeeId) {
                    alert('{{ __("Please select an employee first") }}');
                    return;
                }

                if (!content && !templateId) {
                    alert('{{ __("Please enter content or select a template") }}');
                    return;
                }

                $.post('{{ route('administration.certificate.preview') }}', {
                    _token: '{{ csrf_token() }}',
                    employee_id: employeeId,
                    content: content,
                    certificate_template_id: templateId
                })
                .done(function(response) {
                    if (response.success) {
                        $('#contentPreview').html(response.content);
                    }
                })
                .fail(function(xhr) {
                    alert('{{ __("Error generating preview") }}');
                });
            });
        });

        function insertPlaceholder(placeholder) {
            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;
            
            textarea.value = text.substring(0, start) + placeholder + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
        }
    </script>
@endsection
