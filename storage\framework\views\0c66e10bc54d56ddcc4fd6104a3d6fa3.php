<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Permissions')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Permissions')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Role & Permission')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Permission')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Permissions')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">All Permissions</h5>
        
                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.settings.rolepermission.permission.create')); ?>" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                        Create Permission
                    </a>
                </div>
            </div>
            <div class="card-body">
                <table class="table data-table table-bordered table-responsive" style="width: 100%;">
                    <thead>
                        <tr>
                            <th>Sl.</th>
                            <th>Module</th>
                            <th>Permissions</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                            <tr>
                                <th><?php echo e(serial($modules, $key)); ?></th>
                                <td><?php echo e($module->name); ?></td>
                                <td>
                                    <?php $__currentLoopData = $module->permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-label-primary"><?php echo e($permission->name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <a href="javascript:void(0);" class="btn btn-sm btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="text-primary ti ti-dots-vertical"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-end m-0" style="">
                                            <a href="javascript:void(0);" class="dropdown-item">
                                                <i class="text-primary ti ti-pencil"></i> 
                                                Edit
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a href="javascript:void(0);" class="dropdown-item text-danger delete-record confirm-danger">
                                                <i class="ti ti-trash"></i> 
                                                Delete
                                            </a>
                                        </div>
                                    </div>
                                    <a href="<?php echo e(route('administration.settings.rolepermission.permission.module.show', ['module' => $module])); ?>" class="btn btn-sm btn-icon item-edit" data-bs-toggle="tooltip" title="Show Details">
                                        <i class="text-primary ti ti-info-hexagon"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>        
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/permission/index.blade.php ENDPATH**/ ?>